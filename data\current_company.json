{"_agentInstruction": "This document contains the complete Standard Operating Procedures (SOPs) for a specific client. Use the instructions in each section to guide your actions and conversations.", "companyProfile": {"_agentInstruction": "Use this section for a quick, at-a-glance overview of the client's basic information.", "name": "Apex Pros", "location": "Dublin, OH 43016", "owner": "<PERSON>", "timeZone": "EST", "clientSoftware": "Service Titan ACP", "bookingMethod": "", "timeFrame": "AM/PM"}, "hotTopics": {"_agentInstruction": "Check this section for urgent, time-sensitive announcements or temporary changes to procedures that affect booking.", "specialInstructions": ["Manually book same day - leave unassigned.", "Do not quote date or time, advise dispatch will call and confirm the appt details."]}, "companyInformation": {"_agentInstruction": "Refer to this section for detailed, foundational information about the client's business, such as their official name, address, and the trades they cover.", "name": "Apex Pros", "trades": ["HVAC", "Plumbing", "Electrical"], "address": {"street": "4300 Tuller Rd", "cityStateZip": "Dublin, OH 43016"}, "phoneNumber": "************", "owner": "<PERSON>", "established": "2021", "timeZone": "EST", "acquisitions": ["Paramount HVAC", "Joe's Drains"]}, "servicesNotProvided": {"_agentInstruction": "Before booking a job, always check this section to ensure the client performs the requested service. If the service is listed here, do not book it.", "services": ["New Commercial Customers", "Parts/Equipment Sales", "Portable/Window heating and air systems", "Swamp coolers", "Appliance repairs (BOOK install/connection)", "Drain fields", "Irrigation/Sprinklers - Any", "Leach Beds", "Parts/Equipment Sales", "Pools/Hot tubs - any plumbing", "Septic Tanks (pipes to and from - OK to BOOK)", "Septic Tank pumping", "Undermounted sinks", "Appliance repairs - (BOOK install/connection)", "Pools/Hot Tubs - any lighting", "Solar"], "trades": [], "buildingTypes": ["Vehicles/RVs/Boats"], "customerTypes": ["New Commercial HVAC Customers", "Home Warranty Companies"]}, "mustBookAndJobTypes": {"_agentInstruction": "This section defines booking priorities. The 'mustBookRule' is for critical jobs that must be scheduled immediately. 'priorityJobs' outlines other important job types and their specific conditions.", "mustBookRule": "Jobs scheduled, same day, during regular business hours with no current availability in ServiceTitan.", "priorityJobs": [{"jobType": "No heat – only system in home", "servicePriority": "", "details": [{"trade": "", "conditions": []}]}, {"jobType": "No cool – only system in home", "servicePriority": "", "details": [{"trade": "", "conditions": []}]}, {"jobType": "System leaking and causing damage", "servicePriority": "", "details": [{"trade": "", "conditions": []}]}, {"jobType": "Any non-functional equipment installed by company within 7 days", "servicePriority": "", "details": [{"trade": "", "conditions": []}]}, {"jobType": "Clogged toilet (only toilet in home)", "servicePriority": "", "details": [{"trade": "", "conditions": []}]}, {"jobType": "Leak through ceiling/floor", "servicePriority": "", "details": [{"trade": "", "conditions": []}]}, {"jobType": "Gas leak / smelling gas", "servicePriority": "", "details": [{"trade": "", "conditions": []}]}, {"jobType": "No water in the home", "servicePriority": "", "details": [{"trade": "", "conditions": []}]}, {"jobType": "No hot water in the home", "servicePriority": "", "details": [{"trade": "", "conditions": []}]}, {"jobType": "Flooding", "servicePriority": "", "details": [{"trade": "", "conditions": []}]}, {"jobType": "Multi drain sewage or water backup", "servicePriority": "", "details": [{"trade": "", "conditions": []}]}, {"jobType": "Any non-functional equipment installed by company within 7 days", "servicePriority": "", "details": [{"trade": "", "conditions": []}]}, {"jobType": "No power to whole home (not provider outage)", "servicePriority": "", "details": [{"trade": "", "conditions": []}]}, {"jobType": "Sparking / Burning Smell on electric equipment", "servicePriority": "", "details": [{"trade": "", "conditions": []}]}, {"jobType": "Generator not functioning AND no power in all or part of home", "servicePriority": "", "details": [{"trade": "", "conditions": []}]}, {"jobType": "Water in electrical panel", "servicePriority": "", "details": [{"trade": "", "conditions": []}]}, {"jobType": "Any non-functional equipment installed by company within 7 days", "servicePriority": "", "details": [{"trade": "", "conditions": []}]}], "additionalInstructions": ""}, "clientProcedures": {"_agentInstruction": "Follow the procedures in this section for standard client interactions, such as confirming appointments, handling cancellations, and using communication tools like Slack.", "appointmentConfirmation": {"enabled": true, "script": "It looks like you are scheduled on (date) between (four hour window), your tech will reach out when on the way", "internalNote": "Stage 4 - do NOT confirm in Service Titan"}, "memberMaintenance": {"recurringService": true}, "cancellationPolicy": "Cancel", "callClassification": {"required": null}, "slackUsage": ""}, "schedulingRules": {"_agentInstruction": "Use these rules to determine when and how to schedule jobs, including technician assignments, service zones, and any special conditions that apply.", "rules": [{"Scheduling": "Regular Hours", "Hours": "7a- 6p M-F", "Technician(s)": "Unassigned", "Zone(s)": "Dayton Zone\nColumbus Zone", "Special Rules": "No commercial HVAC jobs until further notice"}, {"Scheduling": "AH/Weekends", "Hours": "Not offered", "Technician(s)": "Not offered", "Zone(s)": null, "Special Rules": "Not offered"}, {"Scheduling": "Phone\nConsultations", "Hours": "7a- 6p M-F", "Technician(s)": "Send as message", "Zone(s)": null, "Special Rules": "Offer in person first, if they decline, send as message"}]}, "fees": {"_agentInstruction": "Refer to this section for all pricing information, including service fees for non-members, members, and commercial clients.", "charges": [{"feeType": "Regular Hour", "Non-Member": "HVAC: $69 wwr\nAll other: $0 + rep", "Member Care Bear Club": "All: 0 + rep", "Commercial": "HVAC: $249 (per unit) wwr (NO commercial HVAC\njobs Until Further Notice)\nAll other: $0 + rep"}, {"feeType": "AH/Weekends", "Non-Member": "Not offered", "Member Care Bear Club": "Not offered", "Commercial": "Not offered"}, {"feeType": "<PERSON><PERSON>", "Non-Member": "Tech to quote on site", "Member Care Bear Club": "Included", "Commercial": "Tech to quote on site (NO commercial HVAC jobs\nUntil Further Notice)"}, {"feeType": "Estimates", "Non-Member": null, "Member Care Bear Club": null, "Commercial": "ANY new systems (Plumbing, HVAC, Electric), whole home repiping, panel upgrade\n(NO commercial HVAC jobs Until Further notice)"}]}, "escalationContacts": {"_agentInstruction": "FOR INTERNAL USE ONLY. Use this list when a situation requires escalation to a specific contact at the client's company. Follow the procedure note carefully.", "procedureNote": "- INTERNAL CIC USE ONLY -", "contacts": [{"name": "<PERSON><PERSON>", "phoneNumber": "************"}]}, "referrals": {"_agentInstruction": "Consult this section for the client's policy on providing referrals when a customer needs a service that is not provided.", "policy": ""}}