{"_agentInstruction": "This document contains the complete Standard Operating Procedures (SOPs) for a specific client. Use the instructions in each section to guide your actions and conversations.", "companyProfile": {"_agentInstruction": "Use this section for a quick, at-a-glance overview of the client's basic information.", "name": "", "location": "1026 Fort Worth Hwy", "owner": "<PERSON>, <PERSON>'s Plumbing, Heating, Air & Electrical", "timeZone": "Central", "clientSoftware": "", "bookingMethod": "", "timeFrame": ""}, "hotTopics": {"_agentInstruction": "Check this section for urgent, time-sensitive announcements or temporary changes to procedures that affect booking.", "specialInstructions": []}, "companyInformation": {"_agentInstruction": "Refer to this section for detailed, foundational information about the client's business, such as their official name, address, and the trades they cover.", "name": "", "trades": ["HVAC/Plumbing/Electrical"], "address": {"street": "", "cityStateZip": ""}, "phoneNumber": "", "owner": "", "established": "", "timeZone": "", "acquisitions": []}, "servicesNotProvided": {"_agentInstruction": "Before booking a job, always check this section to ensure the client performs the requested service. If the service is listed here, do not book it.", "services": ["Pools (including pool lights/electrical pool work, pool drains)", "<PERSON><PERSON> Dr<PERSON>", "Electric Baseboard Heat", "Boilers", "Solar / backup battery", "Neon lights", "Christmas Lights", "Irrigation leaks", "Backflow testing", "Shower pan replacement", "Insulation", "Soffit/Siding", "Parts/Equipment Sales", "Mini Splits", "PTAC units", "Portable generator repair", "Repairs for septic / wells / aerobic systems (will not work on power)", "Electric fireplace", "Grease Traps", "Installation of underground water tanks", "Undermount sink installation", "<PERSON><PERSON>", "Sprinkler Systems (Neither sprinkler heads nor water lines)", "Geothermal Systems", "Installation of customer supplied systems HVAC", "Yard lights", "Ceiling fan / exhaust fan repairs - will replace", "Septic repair and pumping", "French drains", "Sealing or installing propane tanks", "PB / poly b pipes - Customer will be told if this is what they have", "Trades:", "Vehicles / RVs / Boats", "New construction", "Building Types:", "Customer Types: Home Warranties"], "trades": [], "buildingTypes": [], "customerTypes": []}, "mustBookAndJobTypes": {"_agentInstruction": "This section defines booking priorities. The 'mustBookRule' is for critical jobs that must be scheduled immediately. 'priorityJobs' outlines other important job types and their specific conditions.", "mustBookRule": "Jobs scheduled, same day, during regular business hours with no current availability in ServiceTitan.", "priorityJobs": [{"jobType": "", "servicePriority": "", "details": [{"trade": "", "conditions": []}]}], "additionalInstructions": ""}, "clientProcedures": {"_agentInstruction": "Follow the procedures in this section for standard client interactions, such as confirming appointments, handling cancellations, and using communication tools like Slack.", "appointmentConfirmation": {"enabled": null, "script": "", "internalNote": ""}, "memberMaintenance": {"recurringService": null}, "cancellationPolicy": "", "callClassification": {"required": null}, "slackUsage": ""}, "schedulingRules": {"_agentInstruction": "Use these rules to determine when and how to schedule jobs, including technician assignments, service zones, and any special conditions that apply.", "rules": [{"type": "", "schedule": "", "technician": "", "zones": null, "tags": null, "specialRules": ""}]}, "fees": {"_agentInstruction": "Refer to this section for all pricing information, including service fees for non-members, members, and commercial clients.", "charges": [{"service": "", "nonMember": "", "member": "", "commercial": ""}]}, "escalationContacts": {"_agentInstruction": "FOR INTERNAL USE ONLY. Use this list when a situation requires escalation to a specific contact at the client's company. Follow the procedure note carefully.", "procedureNote": "", "contacts": [{"title": "", "name": "", "contact": "", "instructions": []}]}, "referrals": {"_agentInstruction": "Consult this section for the client's policy on providing referrals when a customer needs a service that is not provided.", "policy": ""}}