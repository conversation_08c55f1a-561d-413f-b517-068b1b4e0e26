#!/usr/bin/env python3
"""
Simple Company Operations Agent Backend
"""

import os
import json
import logging
from flask import Flask, request, jsonify
from flask_cors import CORS
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Set up logging first
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Appointment Setter Assistant - Helps human agents navigate company policies
from agent_tools import CompanyAgent  # Fallback only
from appointment_setter_assistant import AppointmentSetterAssistant
from clean_document_converter import ComprehensiveDocumentConverter

print("🎯 Appointment Setter Assistant - Guiding Human Agents Through Company Policies")

def _is_direct_policy_question(message: str) -> bool:
    """Detect if this is a direct policy question vs appointment guidance request"""
    message_lower = message.lower()

    # Direct policy question indicators
    policy_indicators = [
        "fee", "cost", "price", "charge", "rate",
        "after hours", "weekend", "emergency",
        "what is", "what's", "how much",
        "policy", "rule", "procedure",
        "do you service", "can you", "do we",
        "hours", "time", "schedule",
        "contact", "phone", "escalation"
    ]

    # Customer scenario indicators (should use appointment guidance)
    scenario_indicators = [
        "my", "i have", "i need", "help with",
        "multiple issues", "problems", "not working",
        "broken", "emergency", "tonight", "asap"
    ]

    # Check for policy indicators
    has_policy_indicators = any(indicator in message_lower for indicator in policy_indicators)

    # Check for scenario indicators
    has_scenario_indicators = any(indicator in message_lower for indicator in scenario_indicators)

    # If it has policy indicators but no scenario indicators, treat as direct question
    if has_policy_indicators and not has_scenario_indicators:
        return True

    # If it's a short question (under 50 chars), likely a direct question
    if len(message.strip()) < 50 and has_policy_indicators:
        return True

    return False

# Logging already set up above

# Initialize Flask app
app = Flask(__name__)
CORS(app)

# Global variables
current_agent = None
current_company_data = None
document_converter = ComprehensiveDocumentConverter()

@app.route('/', methods=['GET'])
def home():
    """Home page with API documentation"""
    return """
    <h1>Company Operations Agent API</h1>
    <p>Backend is running successfully!</p>
    <h2>Available Endpoints:</h2>
    <ul>
        <li><strong>POST /upload-json</strong> - Upload company JSON data</li>
        <li><strong>POST /chat</strong> - Chat with the agent</li>
        <li><strong>GET /company-data</strong> - Get company information</li>
        <li><strong>GET /health</strong> - Health check</li>
    </ul>
    """

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({"status": "healthy", "message": "Company Agent API is running"})

@app.route('/upload-json', methods=['POST'])
def upload_json():
    """Upload company JSON data to configure the agent"""
    global current_agent, current_company_data
    
    try:
        # Get JSON data from request
        if 'file' in request.files:
            # Handle file upload
            file = request.files['file']
            if file.filename == '':
                return jsonify({"error": "No file selected"}), 400
            
            if file and file.filename.endswith('.json'):
                content = file.read().decode('utf-8')
                company_data = json.loads(content)
            else:
                return jsonify({"error": "Please upload a valid JSON file"}), 400
        else:
            # Handle JSON data directly
            company_data = request.get_json()
            if not company_data:
                return jsonify({"error": "No JSON data provided"}), 400
        
        # Store the company data
        current_company_data = company_data
        
        # Initialize the appointment setter assistant
        logger.info("Creating appointment setter assistant...")

        # Try appointment setter assistant first, fallback to basic agent
        try:
            current_agent = AppointmentSetterAssistant(company_data)
            agent_type = "Appointment Setter Assistant (Company Policy Navigator)"
            logger.info("✅ Appointment setter assistant initialized")
        except Exception as e:
            logger.warning(f"Assistant failed, using basic agent: {e}")
            current_agent = CompanyAgent(company_data)
            agent_type = "Basic Agent (Assistant unavailable)"

        # Extract company name for response
        company_name = "Unknown Company"
        if 'companyProfile' in company_data:
            company_name = company_data['companyProfile'].get('name', company_name)
        elif 'companyInformation' in company_data:
            company_name = company_data['companyInformation'].get('name', company_name)

        logger.info(f"Successfully loaded {company_name} with {agent_type}")

        return jsonify({
            "message": "Company data uploaded successfully",
            "company_name": company_name,
            "agent_type": agent_type,
            "vector_search_enabled": hasattr(current_agent, 'vector_store'),
            "business_isolation": "GUARANTEED - Business-specific data only",
            "sections_loaded": list(company_data.keys())
        })
        
    except json.JSONDecodeError:
        return jsonify({"error": "Invalid JSON format"}), 400
    except Exception as e:
        return jsonify({"error": f"An error occurred: {str(e)}"}), 500

@app.route('/company-data', methods=['GET'])
def get_company_data():
    """Get current company information"""
    global current_company_data

    try:
        if not current_company_data:
            return jsonify({"loaded": False, "error": "No company data loaded"})

        # Extract key information
        company_profile = current_company_data.get("companyProfile", {})
        company_info = current_company_data.get("companyInformation", {})
        hot_topics = current_company_data.get("hotTopics", {}).get("specialInstructions", [])

        return jsonify({
            "loaded": True,
            "company_name": company_profile.get("name", "Unknown"),
            "location": company_info.get("address", {}).get("cityStateZip", "Unknown"),
            "trades": company_info.get("trades", []),
            "hot_topics": hot_topics,
            "sections": list(current_company_data.keys())
        })

    except Exception as e:
        return jsonify({"loaded": False, "error": f"An error occurred: {str(e)}"})

@app.route('/clear-data', methods=['POST'])
def clear_company_data():
    """Clear all company data and embeddings"""
    global current_company_data, current_agent

    try:
        # Get company ID before clearing
        company_id = None
        if current_agent and hasattr(current_agent, 'company_id'):
            company_id = current_agent.company_id

        # Clear global variables
        current_company_data = None
        current_agent = None

        # Clear any cached data
        logger.info("Company data cleared successfully")

        return jsonify({
            "success": True,
            "message": "Company data cleared successfully"
        })
    except Exception as e:
        logger.error(f"Error clearing data: {str(e)}")
        return jsonify({"error": f"Error clearing data: {str(e)}"}), 500



@app.route('/chat', methods=['POST'])
def chat():
    """Chat with the company agent"""
    global current_agent
    
    try:
        if not current_agent:
            return jsonify({"error": "No company data loaded. Please upload JSON data first."}), 400
        
        data = request.get_json()
        if not data or 'message' not in data:
            return jsonify({"error": "No message provided"}), 400
        
        user_message = data['message']
        
        # Get response from the agent
        if hasattr(current_agent, 'guide_appointment_setter'):
            # Check if this is a direct policy question or appointment guidance request
            if _is_direct_policy_question(user_message):
                # Use policy search for direct questions
                response = current_agent.search_company_policies(user_message)
            else:
                # Use appointment setter assistant for complex scenarios
                response = current_agent.guide_appointment_setter(user_message, "non-member")
        elif hasattr(current_agent, 'query'):
            # Use enhanced agent
            response = current_agent.query(user_message)
        else:
            # Use basic agent
            response = current_agent.agent_executor.invoke({"input": user_message})
            response = response.get("output", "No response generated")

        return jsonify({
            "response": response,
            "company_name": current_company_data.get("companyProfile", {}).get("name", "Unknown")
        })

    except Exception as e:
        return jsonify({"error": f"An error occurred: {str(e)}"}), 500

@app.route('/appointment-guidance', methods=['POST'])
def appointment_guidance():
    """Provide step-by-step guidance for appointment setters"""
    global current_agent

    try:
        if not current_agent:
            return jsonify({"error": "No company data loaded. Please upload JSON data first."}), 400

        data = request.get_json()
        if not data or 'customer_request' not in data:
            return jsonify({"error": "Customer request is required"}), 400

        customer_request = data['customer_request']
        customer_type = data.get('customer_type', 'non-member')

        # Check if we have the appointment setter assistant
        if hasattr(current_agent, 'guide_appointment_setter'):
            guidance = current_agent.guide_appointment_setter(customer_request, customer_type)

            return jsonify({
                "guidance": guidance,
                "agent_type": "Appointment Setter Assistant",
                "company": current_agent.business_name,
                "parent_company": current_agent.parent_company
            })
        else:
            # Fallback for basic agent
            return jsonify({
                "guidance": "Appointment setter assistant not available. Please use the chat interface.",
                "fallback": True
            })

    except Exception as e:
        return jsonify({"error": f"An error occurred: {str(e)}"}), 500

@app.route('/policy-search', methods=['POST'])
def policy_search():
    """Search company policies for specific guidance"""
    global current_agent

    try:
        if not current_agent:
            return jsonify({"error": "No company data loaded. Please upload JSON data first."}), 400

        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({"error": "Search query is required"}), 400

        query = data['query']

        # Check if we have the appointment setter assistant
        if hasattr(current_agent, 'search_company_policies'):
            results = current_agent.search_company_policies(query)

            return jsonify({
                "results": results,
                "query": query,
                "company": current_agent.business_name
            })
        else:
            return jsonify({
                "results": "Policy search not available with current agent.",
                "fallback": True
            })

    except Exception as e:
        return jsonify({"error": f"An error occurred: {str(e)}"}), 500

@app.route('/convert-document', methods=['POST'])
def convert_document():
    """Convert document text to comprehensive operations JSON"""
    global document_converter

    try:
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({"error": "Document text is required"}), 400

        document_text = data['text']
        company_name = data.get('company_name', None)

        # Convert document to comprehensive JSON
        logger.info("Converting document to comprehensive operations JSON...")
        operations_json = document_converter.convert_document_to_operations_json(
            document_text, company_name
        )

        logger.info("Document conversion completed successfully")

        return jsonify({
            "success": True,
            "message": "Document converted to comprehensive operations JSON",
            "operations_data": operations_json,
            "sections_included": [
                "Company Profile", "Hot Topics", "Operating Hours",
                "Scheduling Rules", "Company Information", "Services Not Provided",
                "Booking Rules & Job Types", "Fee Schedule", "Client Procedures",
                "Escalation Contacts", "Referral List"
            ]
        })

    except Exception as e:
        logger.error(f"Error converting document: {str(e)}")
        return jsonify({"error": f"Error converting document: {str(e)}"}), 500

@app.route('/upload-text-document', methods=['POST'])
def upload_text_document():
    """Upload text document and convert to comprehensive JSON, then load into agent"""
    global current_agent, current_company_data, document_converter

    try:
        if 'file' not in request.files:
            return jsonify({"error": "No file provided"}), 400

        file = request.files['file']
        if file.filename == '':
            return jsonify({"error": "No file selected"}), 400

        # Read document content
        content = file.read().decode('utf-8')
        company_name = request.form.get('company_name', None)

        # Convert to comprehensive JSON
        logger.info("Converting uploaded document to comprehensive operations JSON...")
        operations_json = document_converter.convert_document_to_operations_json(
            content, company_name
        )

        # Store the data and create appointment setter assistant
        current_company_data = operations_json

        # Try appointment setter assistant first, fallback to basic agent
        try:
            current_agent = AppointmentSetterAssistant(operations_json)
            agent_type = "Appointment Setter Assistant (Company Policy Navigator)"
        except Exception as e:
            logger.warning(f"Assistant failed, using basic agent: {e}")
            current_agent = CompanyAgent(operations_json)
            agent_type = "Basic Agent (Assistant unavailable)"

        company_name = operations_json.get("companyProfile", {}).get("name", "Unknown Company")

        logger.info(f"Successfully converted and loaded {company_name}")

        return jsonify({
            "success": True,
            "message": f"Document converted and loaded for {company_name}",
            "company_name": company_name,
            "sections_loaded": list(operations_json.keys()),
            "comprehensive_data": True
        })

    except Exception as e:
        logger.error(f"Error uploading and converting document: {str(e)}")
        return jsonify({"error": f"Error processing document: {str(e)}"}), 500

@app.route('/test-business-search', methods=['POST'])
def test_business_search():
    """Test business-focused semantic search"""
    global current_agent

    try:
        if not current_agent:
            return jsonify({"error": "No company data loaded"}), 400

        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({"error": "Query is required"}), 400

        query = data['query']

        # Test if enhanced agent is available
        if hasattr(current_agent, 'search_business_context'):
            # Enhanced agent with semantic search
            search_results = current_agent.search_business_context(query, max_results=5)
            business_summary = current_agent.get_business_summary()

            return jsonify({
                "success": True,
                "query": query,
                "business_name": current_agent.business_name,
                "parent_company": current_agent.parent_company,
                "search_results": search_results,
                "business_summary": business_summary,
                "search_type": "Enhanced Semantic Search with Business Isolation",
                "results_count": len(search_results)
            })
        else:
            # Basic agent fallback
            response = current_agent.query(query)
            return jsonify({
                "success": True,
                "query": query,
                "response": response,
                "search_type": "Basic JSON Search",
                "note": "Enhanced search not available"
            })

    except Exception as e:
        logger.error(f"Error in business search test: {str(e)}")
        return jsonify({"error": f"Search test failed: {str(e)}"}), 500

@app.route('/business-summary', methods=['GET'])
def get_business_summary():
    """Get comprehensive business summary"""
    global current_agent

    try:
        if not current_agent:
            return jsonify({"error": "No company data loaded"}), 400

        if hasattr(current_agent, 'get_business_summary'):
            summary = current_agent.get_business_summary()
            return jsonify({
                "success": True,
                "summary": summary
            })
        else:
            # Basic agent fallback
            company_name = current_company_data.get("companyProfile", {}).get("name", "Unknown")
            return jsonify({
                "success": True,
                "summary": {
                    "business_name": company_name,
                    "agent_type": "Basic Agent",
                    "vector_search_enabled": False
                }
            })

    except Exception as e:
        logger.error(f"Error getting business summary: {str(e)}")
        return jsonify({"error": f"Failed to get summary: {str(e)}"}), 500

if __name__ == '__main__':
    print("🚀 Starting Company Operations Agent Backend...")
    print("📡 Backend available at: http://localhost:5000")
    print("🌐 Open company-agent-interface.html in your browser")
    print("⏹️  Press Ctrl+C to stop the server")
    app.run(debug=False, port=5000)
