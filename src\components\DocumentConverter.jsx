import React, { useState, useRef, useCallback } from 'react';

// Helper components for UI
const FileInput = ({ onFileSelect, disabled }) => (
    <div className="w-full">
        <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-[#181818] border-dashed rounded-2xl bg-[#111] hover:bg-[#181818] transition-all duration-200 shadow-lg shadow-black/10">
            <div className="space-y-1 text-center">
                <svg className="mx-auto h-12 w-12 text-[#666]" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                    <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                </svg>
                <div className="flex text-sm text-[#888]">
                    <label htmlFor="file-upload-input" className="relative cursor-pointer bg-[#222] hover:bg-[#333] rounded-lg font-medium text-[#22c55e] hover:text-white focus-within:outline-none focus-within:ring-2 focus-within:ring-[#22c55e] px-2 py-1 transition-all duration-200">
                        <span>Upload a file</span>
                        <input id="file-upload-input" name="file-upload" type="file" className="sr-only" onChange={onFileSelect} accept=".csv,.txt,.text" disabled={disabled} />
                    </label>
                    <p className="pl-1">or drag and drop</p>
                </div>
                <p className="text-xs text-[#666]">CSV, TXT files</p>
            </div>
        </div>
    </div>
);

const ConvertButton = ({ onClick, disabled, isLoading }) => (
    <button
        type="button"
        onClick={onClick}
        disabled={disabled || isLoading}
        className="w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-sm font-medium rounded-2xl shadow-lg shadow-black/10 bg-white text-black hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-[#22c55e] disabled:bg-[#222] disabled:text-[#666] disabled:cursor-not-allowed hover:scale-105 active:scale-95 transition-all duration-200"
    >
        {isLoading ? (
            <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Processing...
            </>
        ) : (
            'Convert to JSON'
        )}
    </button>
);

// Modal Component
const Modal = ({ show, onClose, title, content, isLoading }) => {
    if (!show) return null;

    return (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[10000]">
            <div className="bg-[#111] rounded-2xl w-full max-w-md p-6 relative animate-fade-in z-[10001] mx-4">
                <div className="flex items-center justify-between mb-6">
                    <h2 className="text-lg font-semibold text-white tracking-tight">{title}</h2>
                    <button
                        onClick={onClose}
                        className="text-[#888] hover:text-white transition-colors duration-200 p-1"
                    >
                        <svg className="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                        </svg>
                    </button>
                </div>
                
                {isLoading ? (
                    <div className="flex items-center justify-center py-8">
                        <div className="flex items-center gap-3 text-[#888]">
                            <svg className="animate-spin w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h12v11H4V4z" clipRule="evenodd" />
                            </svg>
                            <span className="text-sm">Processing...</span>
                        </div>
                    </div>
                ) : (
                    <div className="text-[#888] text-sm leading-relaxed whitespace-pre-wrap max-h-96 overflow-y-auto">
                        {content}
                    </div>
                )}
            </div>
        </div>
    );
};

const JsonOutput = ({ jsonOutput, error, onExportJSON, onSummarize, onGenerateReport, onGenerateQuiz, isLoading }) => {
    if (error) {
        return (
            <div className="w-full bg-[#111] border border-red-500 text-red-500 px-4 py-3 rounded-xl shadow-lg animate-fade-in flex items-start gap-3" role="alert">
                <svg className="w-5 h-5 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                </svg>
                <div>
                    <strong className="font-semibold">Error: </strong>
                    <span className="block sm:inline">{error}</span>
                </div>
            </div>
        );
    }

    if (!jsonOutput) {
        return null;
    }

    return (
        <div className="space-y-4">
            <div className="bg-[#111] rounded-2xl p-6 shadow-lg shadow-black/10">
                <div className="flex items-center justify-between mb-4">
                    <h3 className="text-lg font-semibold text-white tracking-tight">JSON Output</h3>
                    <button
                        onClick={onExportJSON}
                        className="bg-[#22c55e] hover:bg-[#22c55e]/80 text-white px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 hover:scale-105 active:scale-95"
                    >
                        Export JSON
                    </button>
                </div>
                <pre className="bg-[#0A0A0A] p-4 rounded-xl text-[#888] text-xs overflow-x-auto max-h-96 border border-[#181818]">
                    {jsonOutput}
                </pre>
            </div>

            <div className="bg-[#111] rounded-2xl p-6 shadow-lg shadow-black/10">
                <h3 className="text-lg font-semibold text-white tracking-tight mb-4">AI Analysis Tools</h3>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <button
                        onClick={onSummarize}
                        disabled={isLoading}
                        className="bg-[#222] hover:bg-[#333] text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                    >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clipRule="evenodd" />
                        </svg>
                        Summarize
                    </button>
                    <button
                        onClick={onGenerateReport}
                        disabled={isLoading}
                        className="bg-[#222] hover:bg-[#333] text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                    >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 0v12h8V4H6z" clipRule="evenodd" />
                        </svg>
                        Generate Report
                    </button>
                    <button
                        onClick={onGenerateQuiz}
                        disabled={isLoading}
                        className="bg-[#222] hover:bg-[#333] text-white px-4 py-3 rounded-xl text-sm font-medium transition-all duration-200 hover:scale-105 active:scale-95 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center gap-2"
                    >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M9.243 3.03a1 1 0 01.727 1.213L9.53 6h2.94l.56-2.243a1 1 0 111.94.486L14.53 6H17a1 1 0 110 2h-2.97l-1 4H15a1 1 0 110 2h-2.47l-.56 2.242a1 1 0 11-1.94-.485L10.47 14H7.53l-.56 2.242a1 1 0 11-1.94-.485L5.47 14H3a1 1 0 110-2h2.97l1-4H5a1 1 0 110-2h2.47l.56-2.243a1 1 0 011.213-.727zM9.03 8l-1 4h2.94l1-4H9.03z" clipRule="evenodd" />
                        </svg>
                        Create Quiz
                    </button>
                </div>
            </div>
        </div>
    );
};

// Main Document Converter Component
export default function DocumentConverter() {
    const [fileContent, setFileContent] = useState('');
    const [pastedText, setPastedText] = useState('');
    const [activeTab, setActiveTab] = useState('upload');
    const [fileName, setFileName] = useState('');
    const [jsonOutput, setJsonOutput] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const [error, setError] = useState('');
    
    // State for new features
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [modalContent, setModalContent] = useState('');
    const [modalTitle, setModalTitle] = useState('');
    const [isAnalysisLoading, setIsAnalysisLoading] = useState(false);

    const handleFileSelect = (event) => {
        const file = event.target.files[0];
        if (file) {
            setFileName(file.name);
            const reader = new FileReader();
            reader.onload = (e) => {
                setFileContent(e.target.result);
                setError('');
            };
            reader.readAsText(file);
        }
    };

    const callGeminiApi = useCallback(async (prompt, loadingSetter, contentSetter) => {
        loadingSetter(true);
        setError('');

        const apiKey = "AIzaSyBI6aQ3PaE8T9z3hQGac4aEHisHcCExVBU";
        const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${apiKey}`;

        try {
            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({ contents: [{ parts: [{ text: prompt }] }] }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error?.message || `API request failed with status ${response.status}`);
            }

            const data = await response.json();

            if (data.candidates && data.candidates.length > 0) {
                const textContent = data.candidates[0].content.parts[0].text;
                contentSetter(textContent);
            } else {
                throw new Error('The AI did not return any content.');
            }
        } catch (err) {
            console.error(err);
            setError(`An error occurred: ${err.message}`);
            if (isModalOpen) {
                setModalContent(`An error occurred: ${err.message}`);
            }
        } finally {
            loadingSetter(false);
        }
    }, [isModalOpen]);

    const convertToJSON = () => {
        const textToConvert = activeTab === 'upload' ? fileContent : pastedText;

        if (!textToConvert.trim()) {
            setError('Please provide text content to convert');
            return;
        }

        // This is the template that will guide the AI's output.
        const jsonTemplate = {
              "companyProfile": { "name": "", "location": "", "owner": "", "timeZone": "", "clientSoftware": "", "bookingMethod": "", "timeFrame": "" },
              "hotTopics": { "specialInstructions": [] },
              "companyInformation": { "name": "", "trades": [], "address": { "street": "", "cityStateZip": "" }, "phoneNumber": "", "owner": "", "established": "", "timeZone": "", "acquisitions": [] },
              "servicesNotProvided": { "services": [], "trades": [], "buildingTypes": [], "customerTypes": [] },
              "mustBookAndJobTypes": { "mustBookRule": "", "priorityJobs": [ { "jobType": "", "servicePriority": "", "details": [ { "trade": "HVAC", "conditions": [] }, { "trade": "Plumbing", "conditions": [] }, { "trade": "Electric", "conditions": [] } ] } ], "additionalInstructions": "" }
            };

        const conversionPrompt = `
            You are an expert data extraction and conversion specialist.
            Your task is to populate the following JSON template with information extracted from the provided text content.
            Adhere strictly to the structure of the template. Do not add, remove, or rename keys.
            If a piece of information isn't available in the text, leave the corresponding value as an empty string "" or an empty array [].

            JSON TEMPLATE:
            ---
            ${JSON.stringify(jsonTemplate, null, 2)}
            ---

            TEXT CONTENT TO PARSE:
            ---
            ${textToConvert}
            ---

            Your final output must ONLY be the populated JSON object, without any surrounding text, explanations, or markdown.
        `;

        callGeminiApi(conversionPrompt, setIsLoading, (rawJson) => {
             const cleanedJson = rawJson.replace(/```json/g, '').replace(/```/g, '').trim();
             try {
                 const parsedJson = JSON.parse(cleanedJson);
                 setJsonOutput(JSON.stringify(parsedJson, null, 2));
             } catch (e) {
                 setError("AI returned invalid JSON. Showing raw output.");
                 setJsonOutput(cleanedJson);
             }
        });
    };

    const handleExportJSON = () => {
        if (!jsonOutput) {
            alert('No JSON data to export. Please convert some data first.');
            return;
        }

        try {
            // Parse and re-stringify to ensure valid JSON
            const parsedJson = JSON.parse(jsonOutput);
            const formattedJson = JSON.stringify(parsedJson, null, 2);

            // Create blob and download
            const blob = new Blob([formattedJson], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const link = document.createElement('a');

            // Generate filename with timestamp
            const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
            const companyName = parsedJson.companyProfile?.name || 'company';
            const filename = `${companyName.replace(/[^a-zA-Z0-9]/g, '_')}_operations_${timestamp}.json`;

            link.href = url;
            link.download = filename;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            URL.revokeObjectURL(url);

            // Show success message
            alert(`✅ JSON exported successfully as: ${filename}`);
        } catch (error) {
            alert('❌ Error exporting JSON: Invalid JSON format');
        }
    };

    const handleSummarize = () => {
        if (!jsonOutput) {
            alert('Please convert text to JSON first');
            return;
        }

        const summaryPrompt = `Based on the following JSON data, provide a concise bullet-point summary of the key information.
        JSON Data:\n---\n${jsonOutput}\n---`;
        setModalTitle("AI Summary");
        setModalContent('');
        setIsModalOpen(true);
        callGeminiApi(summaryPrompt, setIsAnalysisLoading, setModalContent);
    };

    const handleGenerateReport = () => {
        if (!jsonOutput) {
            alert('Please convert text to JSON first');
            return;
        }

        const reportPrompt = `You are a business analyst. Based on the following JSON data from a company's operational guide, write a concise business report. Use a professional tone and markdown formatting.
        JSON Data:\n---\n${jsonOutput}\n---`;
        setModalTitle("AI-Generated Report");
        setModalContent('');
        setIsModalOpen(true);
        callGeminiApi(reportPrompt, setIsAnalysisLoading, setModalContent);
    };

    const handleGenerateQuiz = () => {
        if (!jsonOutput) {
            alert('Please convert text to JSON first');
            return;
        }

        const quizPrompt = `You are a training manager. Based on the following JSON data from a company's operational guide, create a 5-question multiple-choice quiz. For each question, provide 4 options (A, B, C, D) and indicate the correct answer.
        JSON Data:\n---\n${jsonOutput}\n---`;
        setModalTitle("AI-Generated Onboarding Quiz");
        setModalContent('');
        setIsModalOpen(true);
        callGeminiApi(quizPrompt, setIsAnalysisLoading, setModalContent);
    };

    return (
        <div className="w-full max-w-4xl mx-auto px-8">
            <div className="bg-[#111] shadow-xl shadow-black/20 rounded-2xl p-8">
                <div className="text-center mb-8">
                    <h1 className="text-3xl font-semibold text-white tracking-tight mb-2">AI-Powered Document Converter & Analyzer</h1>
                    <p className="text-[#888] text-sm leading-relaxed">Upload or paste text to convert, then summarize, report, or create quizzes.</p>
                </div>

                <div className="space-y-6">
                    <div>
                        <div className="border-b border-[#181818]">
                            <nav className="-mb-px flex space-x-8" aria-label="Tabs">
                                <button
                                    onClick={() => setActiveTab('upload')}
                                    className={`${activeTab === 'upload'
                                        ? 'bg-gradient-to-r from-[#22c55e]/10 to-transparent text-white shadow-lg shadow-black/10'
                                        : 'text-[#888] hover:bg-[#111] hover:text-white hover:shadow-lg hover:shadow-black/10'
                                    } whitespace-nowrap py-4 px-4 border-b-2 border-transparent font-medium text-sm rounded-t-lg transition-all duration-200`}
                                >
                                    Upload File
                                </button>
                                <button
                                    onClick={() => setActiveTab('paste')}
                                    className={`${activeTab === 'paste'
                                        ? 'bg-gradient-to-r from-[#22c55e]/10 to-transparent text-white shadow-lg shadow-black/10'
                                        : 'text-[#888] hover:bg-[#111] hover:text-white hover:shadow-lg hover:shadow-black/10'
                                    } whitespace-nowrap py-4 px-4 border-b-2 border-transparent font-medium text-sm rounded-t-lg transition-all duration-200`}
                                >
                                    Paste Text
                                </button>
                            </nav>
                        </div>

                        <div className="mt-6">
                            {activeTab === 'upload' ? (
                                <div className="space-y-4">
                                    <FileInput onFileSelect={handleFileSelect} disabled={isLoading} />
                                    {fileName && (
                                        <div className="text-sm text-[#888] bg-[#0A0A0A] p-3 rounded-lg border border-[#181818]">
                                            <span className="text-[#22c55e] font-medium">Selected file:</span> {fileName}
                                        </div>
                                    )}
                                </div>
                            ) : (
                                <div className="space-y-4">
                                    <textarea
                                        value={pastedText}
                                        onChange={(e) => setPastedText(e.target.value)}
                                        placeholder="Paste your text content here..."
                                        className="w-full bg-[#111] rounded-2xl p-6 text-white resize-none h-48 focus:outline-none focus:ring-1 focus:ring-[#222] text-sm leading-relaxed transition-shadow duration-200 shadow-lg shadow-black/10"
                                        disabled={isLoading}
                                    />
                                </div>
                            )}
                        </div>
                    </div>

                    <ConvertButton 
                        onClick={convertToJSON} 
                        disabled={activeTab === 'upload' ? !fileContent : !pastedText.trim()} 
                        isLoading={isLoading}
                    />
                </div>

                <div className="mt-8">
                    <JsonOutput
                        jsonOutput={jsonOutput}
                        error={error}
                        onExportJSON={handleExportJSON}
                        onSummarize={handleSummarize}
                        onGenerateReport={handleGenerateReport}
                        onGenerateQuiz={handleGenerateQuiz}
                        isLoading={isAnalysisLoading || isLoading}
                    />
                </div>
            </div>
            <footer className="text-center mt-6 text-[#666] text-xs">
                <p>Built with React & Gemini. API access is handled automatically.</p>
            </footer>
            
            <Modal
                show={isModalOpen}
                onClose={() => setIsModalOpen(false)}
                title={modalTitle}
                content={modalContent}
                isLoading={isAnalysisLoading}
            />
        </div>
    );
}
