<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>CSV to JSON AI Converter</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'chad-bg': '#000000',
                        'chad-surface': '#0A0A0A',
                        'chad-card': '#111111',
                        'chad-elevated': '#181818',
                        'chad-border': '#181818',
                        'chad-accent': '#22c55e',
                        'chad-text-primary': '#ffffff',
                        'chad-text-secondary': '#888888',
                        'chad-text-muted': '#666666'
                    },
                    fontFamily: {
                        'system': ['-apple-system', 'BlinkMacSystemFont', '"Segoe UI"', 'system-ui', 'sans-serif']
                    },
                    animation: {
                        'fade-in': 'fadeIn 0.3s ease-out',
                        'slide-in': 'slideIn 0.3s ease-out'
                    },
                    keyframes: {
                        fadeIn: {
                            '0%': { opacity: '0', transform: 'translateY(8px)' },
                            '100%': { opacity: '1', transform: 'translateY(0)' }
                        },
                        slideIn: {
                            '0%': { opacity: '0', transform: 'translateX(-8px)' },
                            '100%': { opacity: '1', transform: 'translateX(0)' }
                        }
                    }
                }
            }
        }
    </script>
    <style>
        /* Chad GPT Global Styles */
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', system-ui, sans-serif;
            margin: 0;
            padding: 0;
            background: #000000;
            color: #ffffff;
            -webkit-font-smoothing: antialiased;
        }
        
        /* Custom scrollbar styling */
        ::-webkit-scrollbar {
            width: 4px;
            background: transparent;
        }
        
        ::-webkit-scrollbar-thumb {
            background: #222;
            border-radius: 2px;
            border: 1px solid #181818;
        }
        
        ::-webkit-scrollbar-thumb:hover {
            background: #444;
        }
        
        /* Text selection styling */
        ::selection {
            background: rgba(34, 197, 94, 0.15);
            color: #fff;
        }
        
        /* Focus visible styling */
        :focus-visible {
            outline: 2px solid #22c55e;
            outline-offset: 2px;
            border-radius: 12px;
        }
        
        /* Custom animations */
        @keyframes fadeIn {
            0% {
                opacity: 0;
                transform: translateY(8px);
            }
            100% {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        @keyframes slideIn {
            0% {
                opacity: 0;
                transform: translateX(-8px);
            }
            100% {
                opacity: 1;
                transform: translateX(0);
            }
        }
        
        .animate-fade-in {
            animation: fadeIn 0.3s ease-out;
        }
        
        .animate-slide-in {
            animation: slideIn 0.3s ease-out;
        }
    </style>
</head>
<body>
    <div id="root"></div>
    
    <script type="text/babel">
        const { useState, useCallback } = React;
        
        // Helper components for UI
        const FileInput = ({ onFileSelect, disabled, acceptTypes, description, icon }) => (
            <div className="w-full">
                <div className="mt-1 flex justify-center px-6 pt-5 pb-6 border-2 border-[#181818] border-dashed rounded-2xl bg-[#111] hover:bg-[#181818] transition-all duration-200 shadow-lg shadow-black/10">
                    <div className="space-y-1 text-center">
                        {icon}
                        <div className="flex text-sm text-[#888]">
                            <label htmlFor={`file-upload-input-${acceptTypes.replace(/[^a-zA-Z]/g, '')}`} className="relative cursor-pointer bg-[#222] hover:bg-[#333] rounded-lg font-medium text-[#22c55e] hover:text-white focus-within:outline-none focus-within:ring-2 focus-within:ring-[#22c55e] px-2 py-1 transition-all duration-200">
                                <span>Upload a file</span>
                                <input
                                    id={`file-upload-input-${acceptTypes.replace(/[^a-zA-Z]/g, '')}`}
                                    name="file-upload"
                                    type="file"
                                    className="sr-only"
                                    onChange={onFileSelect}
                                    accept={acceptTypes}
                                    disabled={disabled}
                                />
                            </label>
                            <p className="pl-1">or drag and drop</p>
                        </div>
                        <p className="text-xs text-[#666]">{description}</p>
                    </div>
                </div>
            </div>
        );
        
        const ConvertButton = ({ onClick, disabled, isLoading }) => (
            <button
                type="button"
                onClick={onClick}
                disabled={disabled || isLoading}
                className="w-full inline-flex justify-center items-center px-6 py-3 border border-transparent text-sm font-medium rounded-2xl shadow-lg shadow-black/10 bg-white text-black hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-[#22c55e] disabled:bg-[#222] disabled:text-[#666] disabled:cursor-not-allowed hover:scale-105 active:scale-95 transition-all duration-200"
            >
                {isLoading ? (
                    <>
                        <svg className="animate-spin -ml-1 mr-3 h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                        Processing...
                    </>
                ) : (
                    'Convert to JSON'
                )}
            </button>
        );
        
        const JsonOutput = ({ jsonOutput, error, onExportJSON, onSummarize, onGenerateReport, onGenerateQuiz, isLoading }) => {
            if (error) {
                return (
                    <div className="w-full bg-[#111] border border-red-500 text-red-500 px-4 py-3 rounded-xl shadow-lg animate-fade-in flex items-start gap-3" role="alert">
                        <svg className="w-5 h-5 mt-0.5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                        </svg>
                        <div>
                            <strong className="font-semibold">Error: </strong>
                            <span className="block sm:inline">{error}</span>
                        </div>
                    </div>
                );
            }
        
            if (!jsonOutput) return null;
        
            return (
                <div className="w-full mt-6">
                    <h3 className="text-lg font-semibold text-white mb-4 tracking-tight">Conversion Result:</h3>
                    <pre className="bg-[#111] text-[#22c55e] p-6 rounded-2xl text-sm overflow-x-auto w-full shadow-lg shadow-black/10 leading-relaxed">
                        <code>{jsonOutput}</code>
                    </pre>
                    <div className="mt-6 grid grid-cols-1 sm:grid-cols-4 gap-4">
                        <button onClick={onExportJSON} disabled={isLoading} className="inline-flex justify-center items-center px-4 py-3 border border-transparent text-sm font-medium rounded-lg shadow-lg shadow-black/10 text-white bg-[#22c55e] hover:bg-[#16a34a] focus:outline-none focus:ring-2 focus:ring-[#22c55e] disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105 active:scale-95 transition-all duration-200">
                            💾 Export JSON
                        </button>
                        <button onClick={onSummarize} disabled={isLoading} className="inline-flex justify-center items-center px-4 py-3 border border-transparent text-sm font-medium rounded-lg shadow-lg shadow-black/10 text-white bg-[#3b82f6] hover:bg-[#2563eb] focus:outline-none focus:ring-2 focus:ring-[#3b82f6] disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105 active:scale-95 transition-all duration-200">
                            ✨ Summarize
                        </button>
                        <button onClick={onGenerateReport} disabled={isLoading} className="inline-flex justify-center items-center px-4 py-3 border border-transparent text-sm font-medium rounded-lg shadow-lg shadow-black/10 text-white bg-[#8b5cf6] hover:bg-[#7c3aed] focus:outline-none focus:ring-2 focus:ring-[#8b5cf6] disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105 active:scale-95 transition-all duration-200">
                            ✨ Generate Report
                        </button>
                        <button onClick={onGenerateQuiz} disabled={isLoading} className="inline-flex justify-center items-center px-4 py-3 border border-transparent text-sm font-medium rounded-lg shadow-lg shadow-black/10 text-white bg-[#22c55e] hover:bg-[#16a34a] focus:outline-none focus:ring-2 focus:ring-[#22c55e] disabled:opacity-50 disabled:cursor-not-allowed hover:scale-105 active:scale-95 transition-all duration-200">
                            ✨ Generate Quiz
                        </button>
                    </div>
                </div>
            );
        };
        
        const Modal = ({ show, onClose, title, content, isLoading }) => {
            if (!show) return null;
            return (
                <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-[10000] p-4">
                    <div className="bg-[#111] rounded-2xl shadow-xl w-full max-w-2xl max-h-[90vh] flex flex-col animate-fade-in z-[10001]">
                        <div className="flex justify-between items-center p-6 border-b border-[#181818]">
                            <h3 className="text-lg font-semibold text-white tracking-tight">{title}</h3>
                            <button 
                                onClick={onClose} 
                                className="text-[#888] hover:text-white text-2xl leading-none hover:bg-[#222] p-2 rounded-lg transition-all duration-200"
                            >
                                &times;
                            </button>
                        </div>
                        <div className="p-6 overflow-y-auto">
                            {isLoading ? (
                                 <div className="flex justify-center items-center h-48">
                                    <div className="flex items-center gap-2 text-[#666] animate-pulse">
                                        <svg className="animate-spin h-8 w-8" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                           <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                           <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                       </svg>
                                       <span className="text-sm">Generating...</span>
                                    </div>
                                 </div>
                            ) : (
                                <div className="text-white text-sm leading-relaxed whitespace-pre-wrap" dangerouslySetInnerHTML={{ __html: content.replace(/\n/g, '<br />') }}></div>
                            )}
                        </div>
                    </div>
                </div>
            );
        };
        
        // Header Navigation Component
        const HeaderNav = () => (
            <div className="bg-[#0A0A0A] border-b border-[#181818] px-8 py-4">
                <div className="max-w-7xl mx-auto flex items-center justify-between">
                    <div className="flex items-center gap-8">
                        <h1 className="text-lg font-semibold text-white tracking-tight">ContractorInCharge</h1>
                        <nav className="flex items-center gap-6">
                            <a
                                href="index-standalone.html"
                                className="text-[#22c55e] hover:text-white text-sm font-medium transition-colors duration-200 flex items-center gap-2"
                            >
                                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                                </svg>
                                Document Converter
                            </a>
                            <a
                                href="company-agent-interface.html"
                                className="text-[#888] hover:text-white text-sm font-medium transition-colors duration-200 flex items-center gap-2"
                            >
                                <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M8 9a3 3 0 100-6 3 3 0 000 6zM8 11a6 6 0 016 6H2a6 6 0 016-6zM16 7a1 1 0 10-2 0v1h-1a1 1 0 100 2h1v1a1 1 0 102 0v-1h1a1 1 0 100-2h-1V7z"/>
                                </svg>
                                Operations Agent
                            </a>
                        </nav>
                    </div>
                    <div className="text-[#666] text-xs">
                        AI-Powered Business Tools
                    </div>
                </div>
            </div>
        );

        // Main App Component
        function App() {
            const [fileContent, setFileContent] = useState('');
            const [pastedText, setPastedText] = useState('');
            const [activeTab, setActiveTab] = useState('upload');
            const [fileName, setFileName] = useState('');
            const [jsonOutput, setJsonOutput] = useState('');
            const [isLoading, setIsLoading] = useState(false);
            const [error, setError] = useState('');
            const [imageData, setImageData] = useState('');

            // State for new features
            const [isModalOpen, setIsModalOpen] = useState(false);
            const [modalContent, setModalContent] = useState('');
            const [modalTitle, setModalTitle] = useState('');
            const [isAnalysisLoading, setIsAnalysisLoading] = useState(false);
        
            const handleFileSelect = (event) => {
                const file = event.target.files[0];
                if (file) {
                    setFileName(file.name);
                    const reader = new FileReader();

                    // Check if it's an image file
                    if (file.type.startsWith('image/')) {
                        reader.onload = (e) => {
                            setImageData(e.target.result);
                            setFileContent(''); // Clear text content
                            setPastedText(''); // Clear pasted text
                            setError('');
                            setJsonOutput('');
                        };
                        reader.readAsDataURL(file);
                    } else {
                        // Handle text files
                        reader.onload = (e) => {
                            setFileContent(e.target.result);
                            setImageData(''); // Clear image data
                            setPastedText(''); // Clear pasted text
                            setError('');
                            setJsonOutput('');
                        };
                        reader.readAsText(file);
                    }
                }
            };

            const handleImageSelect = (event) => {
                const file = event.target.files[0];
                if (file && file.type.startsWith('image/')) {
                    setFileName(file.name);
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        setImageData(e.target.result);
                        setFileContent(''); // Clear text content
                        setPastedText(''); // Clear pasted text
                        setError('');
                        setJsonOutput('');
                    };
                    reader.readAsDataURL(file);
                } else {
                    alert('Please select a valid image file (PNG, JPG, JPEG, GIF, WebP)');
                }
            };
        
            const handlePastedTextChange = (event) => {
                setPastedText(event.target.value);
                setFileContent(''); // Clear file content
                setImageData(''); // Clear image data
                setFileName('');
                setError('');
                setJsonOutput('');
            }
        
            const callGeminiApi = useCallback(async (prompt, loadingSetter, contentSetter, imageData = null) => {
                loadingSetter(true);
                setError('');

                const apiKey = "AIzaSyBI6aQ3PaE8T9z3hQGac4aEHisHcCExVBU";
                const apiUrl = `https://generativelanguage.googleapis.com/v1beta/models/gemini-2.5-flash-preview-05-20:generateContent?key=${apiKey}`;
        
                try {
                    // Prepare the request body
                    let requestBody;

                    if (imageData) {
                        // For image + text requests
                        const base64Data = imageData.split(',')[1]; // Remove data:image/...;base64, prefix
                        const mimeType = imageData.split(';')[0].split(':')[1]; // Extract MIME type

                        requestBody = {
                            contents: [{
                                parts: [
                                    { text: prompt },
                                    {
                                        inline_data: {
                                            mime_type: mimeType,
                                            data: base64Data
                                        }
                                    }
                                ]
                            }]
                        };
                    } else {
                        // For text-only requests
                        requestBody = { contents: [{ parts: [{ text: prompt }] }] };
                    }

                    const response = await fetch(apiUrl, {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(requestBody),
                    });
        
                    if (!response.ok) {
                        const errorData = await response.json();
                        throw new Error(errorData.error?.message || `API request failed with status ${response.status}`);
                    }
        
                    const data = await response.json();
                    
                    if (data.candidates && data.candidates.length > 0) {
                        const textContent = data.candidates[0].content.parts[0].text;
                        contentSetter(textContent);
                    } else {
                        throw new Error('The AI did not return any content.');
                    }
                } catch (err) {
                    console.error(err);
                    setError(`An error occurred: ${err.message}`);
                    if (isModalOpen) {
                        setModalContent(`An error occurred: ${err.message}`);
                    }
                } finally {
                    loadingSetter(false);
                }
            }, [isModalOpen]);
        
            const contentToProcess = activeTab === 'upload' ? (fileContent || imageData) : pastedText;
        
            const handleConvertToJson = () => {
                // This is the template that will guide the AI's output.
                const jsonTemplate = {
                      "companyProfile": { "name": "", "location": "", "owner": "", "timeZone": "", "clientSoftware": "", "bookingMethod": "", "timeFrame": "" },
                      "hotTopics": { "specialInstructions": [] },
                      "companyInformation": { "name": "", "trades": [], "address": { "street": "", "cityStateZip": "" }, "phoneNumber": "", "owner": "", "established": "", "timeZone": "", "acquisitions": [] },
                      "servicesNotProvided": { "services": [], "trades": [], "buildingTypes": [], "customerTypes": [] },
                      "mustBookAndJobTypes": { "mustBookRule": "", "priorityJobs": [ { "jobType": "", "servicePriority": "", "details": [ { "trade": "HVAC", "conditions": [] }, { "trade": "Plumbing", "conditions": [] }, { "trade": "Electric", "conditions": [] } ] } ], "additionalInstructions": "" }
                    };

                let conversionPrompt;

                if (imageData) {
                    // For image processing
                    conversionPrompt = `
                        You are an expert data extraction and conversion specialist with advanced OCR and image analysis capabilities.
                        Your task is to analyze the provided image and extract any text, data, or information visible in it, then populate the following JSON template.

                        The image may contain:
                        - Documents, forms, or reports
                        - Screenshots of data
                        - Handwritten or printed text
                        - Tables, charts, or structured data
                        - Business cards, invoices, or other business documents

                        Extract ALL visible text and data from the image, then organize it according to the JSON template below.
                        Adhere strictly to the structure of the template. Do not add, remove, or rename keys.
                        If a piece of information isn't available in the image, leave the corresponding value as an empty string "" or an empty array [].

                        JSON TEMPLATE:
                        ---
                        ${JSON.stringify(jsonTemplate, null, 2)}
                        ---

                        Analyze the image carefully and extract all relevant information to populate this JSON structure.
                        Your final output must ONLY be the populated JSON object, without any surrounding text, explanations, or markdown.
                    `;
                } else {
                    // For text processing
                    conversionPrompt = `
                        You are an expert data extraction and conversion specialist.
                        Your task is to populate the following JSON template with information extracted from the provided text content.
                        Adhere strictly to the structure of the template. Do not add, remove, or rename keys.
                        If a piece of information isn't available in the text, leave the corresponding value as an empty string "" or an empty array [].

                        JSON TEMPLATE:
                        ---
                        ${JSON.stringify(jsonTemplate, null, 2)}
                        ---

                        TEXT CONTENT TO PARSE:
                        ---
                        ${contentToProcess}
                        ---

                        Your final output must ONLY be the populated JSON object, without any surrounding text, explanations, or markdown.
                    `;
                }

                callGeminiApi(conversionPrompt, setIsLoading, (rawJson) => {
                     const cleanedJson = rawJson.replace(/```json/g, '').replace(/```/g, '').trim();
                     try {
                         const parsedJson = JSON.parse(cleanedJson);
                         setJsonOutput(JSON.stringify(parsedJson, null, 2));
                     } catch (e) {
                         setError("AI returned invalid JSON. Showing raw output.");
                         setJsonOutput(cleanedJson);
                     }
                }, imageData);
            };
        
            const handleSummarize = () => {
                const summaryPrompt = `Based on the following JSON data, provide a concise bullet-point summary of the key information.
                JSON Data:\n---\n${jsonOutput}\n---`;
                setModalTitle("AI Summary");
                setModalContent('');
                setIsModalOpen(true);
                callGeminiApi(summaryPrompt, setIsAnalysisLoading, setModalContent);
            };
        
            const handleGenerateReport = () => {
                const reportPrompt = `You are a business analyst. Based on the following JSON data from a company's operational guide, write a concise business report. Use a professional tone and markdown formatting.
                JSON Data:\n---\n${jsonOutput}\n---`;
                setModalTitle("AI-Generated Report");
                setModalContent('');
                setIsModalOpen(true);
                callGeminiApi(reportPrompt, setIsAnalysisLoading, setModalContent);
            };
        
            const handleGenerateQuiz = () => {
                const quizPrompt = `You are a training manager. Based on the following JSON data from a company's operational guide, create a 5-question multiple-choice quiz. For each question, provide 4 options (A, B, C, D) and indicate the correct answer.
                JSON Data:\n---\n${jsonOutput}\n---`;
                setModalTitle("AI-Generated Onboarding Quiz");
                setModalContent('');
                setIsModalOpen(true);
                callGeminiApi(quizPrompt, setIsAnalysisLoading, setModalContent);
            };

            const handleExportJSON = () => {
                if (!jsonOutput) {
                    alert('No JSON data to export. Please convert some data first.');
                    return;
                }

                try {
                    // Parse and re-stringify to ensure valid JSON
                    const parsedJson = JSON.parse(jsonOutput);
                    const formattedJson = JSON.stringify(parsedJson, null, 2);

                    // Create blob and download
                    const blob = new Blob([formattedJson], { type: 'application/json' });
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');

                    // Generate filename with timestamp
                    const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                    const companyName = parsedJson.companyProfile?.name || 'company';
                    const filename = `${companyName.replace(/[^a-zA-Z0-9]/g, '_')}_operations_${timestamp}.json`;

                    link.href = url;
                    link.download = filename;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);

                    // Show success message
                    alert(`✅ JSON exported successfully as: ${filename}`);
                } catch (error) {
                    alert('❌ Error exporting JSON: Invalid JSON format');
                }
            };
        
            return (
                <div className="bg-black min-h-screen antialiased" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", system-ui, sans-serif' }}>
                    <HeaderNav />
                    <div className="max-w-7xl mx-auto p-8">
                        <div className="bg-[#111] shadow-xl shadow-black/20 rounded-2xl p-8">
        
                            <div className="space-y-6">
                                <div>
                                    <div className="border-b border-[#181818]">
                                        <nav className="-mb-px flex space-x-8" aria-label="Tabs">
                                            <button
                                                onClick={() => setActiveTab('upload')}
                                                className={`${activeTab === 'upload'
                                                    ? 'bg-gradient-to-r from-[#22c55e]/10 to-transparent text-white shadow-lg shadow-black/10'
                                                    : 'text-[#888] hover:bg-[#111] hover:text-white hover:shadow-lg hover:shadow-black/10'
                                                } whitespace-nowrap py-4 px-4 border-b-2 border-transparent font-medium text-sm rounded-t-lg transition-all duration-200`}
                                            >
                                                📄 Text Files
                                            </button>
                                            <button
                                                onClick={() => setActiveTab('image')}
                                                className={`${activeTab === 'image'
                                                    ? 'bg-gradient-to-r from-[#22c55e]/10 to-transparent text-white shadow-lg shadow-black/10'
                                                    : 'text-[#888] hover:bg-[#111] hover:text-white hover:shadow-lg hover:shadow-black/10'
                                                } whitespace-nowrap py-4 px-4 border-b-2 border-transparent font-medium text-sm rounded-t-lg transition-all duration-200`}
                                            >
                                                🖼️ Images
                                            </button>
                                            <button
                                                onClick={() => setActiveTab('paste')}
                                                className={`${activeTab === 'paste'
                                                    ? 'bg-gradient-to-r from-[#22c55e]/10 to-transparent text-white shadow-lg shadow-black/10'
                                                    : 'text-[#888] hover:bg-[#111] hover:text-white hover:shadow-lg hover:shadow-black/10'
                                                } whitespace-nowrap py-4 px-4 border-b-2 border-transparent font-medium text-sm rounded-t-lg transition-all duration-200`}
                                            >
                                                ✏️ Paste Text
                                            </button>
                                        </nav>
                                    </div>
                                    <div className="pt-6">
                                        {activeTab === 'upload' && (
                                            <FileInput
                                                onFileSelect={handleFileSelect}
                                                disabled={isLoading}
                                                acceptTypes=".csv,.txt,.text"
                                                description="CSV, TXT files"
                                                icon={
                                                    <svg className="mx-auto h-12 w-12 text-[#666]" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                                        <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                    </svg>
                                                }
                                            />
                                        )}
                                        {activeTab === 'image' && (
                                            <div>
                                                <FileInput
                                                    onFileSelect={handleImageSelect}
                                                    disabled={isLoading}
                                                    acceptTypes="image/*"
                                                    description="PNG, JPG, JPEG, GIF, WebP images"
                                                    icon={
                                                        <svg className="mx-auto h-12 w-12 text-[#666]" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
                                                            <path d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L32 26M6 18h36v24a2 2 0 01-2 2H8a2 2 0 01-2-2V18zM15 22a3 3 0 11-6 0 3 3 0 016 0z" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                                                        </svg>
                                                    }
                                                />
                                                {imageData && (
                                                    <div className="mt-4 p-4 bg-[#111] rounded-2xl">
                                                        <h4 className="text-sm font-medium text-white mb-2">Image Preview:</h4>
                                                        <img
                                                            src={imageData}
                                                            alt="Uploaded preview"
                                                            className="max-w-full h-auto max-h-64 rounded-lg border border-[#181818]"
                                                        />
                                                    </div>
                                                )}
                                            </div>
                                        )}
                                        {activeTab === 'paste' && (
                                            <textarea
                                                rows="8"
                                                className="w-full bg-[#111] rounded-2xl p-6 text-white resize-none focus:outline-none focus:ring-1 focus:ring-[#222] text-sm leading-relaxed transition-shadow duration-200 shadow-lg shadow-black/10 focus:bg-[#181818] focus:shadow-[0_0_0_1px_rgba(34,197,94,0.2),0_2px_4px_rgba(34,197,94,0.1)]"
                                                placeholder="Paste your document content here..."
                                                value={pastedText}
                                                onChange={handlePastedTextChange}
                                                disabled={isLoading}
                                            ></textarea>
                                        )}
                                    </div>
                                </div>
        
                                {fileName && (activeTab === 'upload' || activeTab === 'image') && (
                                    <p className="text-sm text-center text-[#888]">
                                        Selected file: <span className="font-medium text-[#22c55e]">{fileName}</span>
                                    </p>
                                )}
        
                                <div className="pt-2">
                                   <ConvertButton onClick={handleConvertToJson} disabled={!contentToProcess} isLoading={isLoading} />
                                </div>
                            </div>
                            
                            <div className="mt-8">
                                <JsonOutput
                                    jsonOutput={jsonOutput}
                                    error={error}
                                    onExportJSON={handleExportJSON}
                                    onSummarize={handleSummarize}
                                    onGenerateReport={handleGenerateReport}
                                    onGenerateQuiz={handleGenerateQuiz}
                                    isLoading={isAnalysisLoading || isLoading}
                                />
                            </div>
                        </div>

                        <footer className="text-center mt-8 text-[#666] text-xs">
                            <p>Built with React & Gemini. API access is handled automatically.</p>
                        </footer>
                    </div>

                    <Modal
                        show={isModalOpen}
                        onClose={() => setIsModalOpen(false)}
                        title={modalTitle}
                        content={modalContent}
                        isLoading={isAnalysisLoading}
                    />
                </div>
            );
        }
        
        // Render the app
        ReactDOM.render(<App />, document.getElementById('root'));
    </script>
</body>
</html>
