import React, { useState } from 'react';
import AgentInterface from './components/AgentInterface';
import DocumentConverter from './components/DocumentConverter';

// Navigation Component
const Navigation = ({ currentPage, onPageChange }) => (
    <div className="bg-[#0A0A0A] border-b border-[#181818] px-8 py-4">
        <div className="max-w-7xl mx-auto flex items-center justify-between">
            <div className="flex items-center gap-8">
                <h1 className="text-lg font-semibold text-white tracking-tight">ContractorInCharge</h1>
                <nav className="flex items-center gap-6">
                    <button
                        onClick={() => onPageChange('agent')}
                        className={`${currentPage === 'agent'
                            ? 'text-[#22c55e]'
                            : 'text-[#888] hover:text-white'
                        } text-sm font-medium transition-colors duration-200 flex items-center gap-2`}
                    >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        Company Agent
                    </button>
                    <button
                        onClick={() => onPageChange('converter')}
                        className={`${currentPage === 'converter'
                            ? 'text-[#22c55e]'
                            : 'text-[#888] hover:text-white'
                        } text-sm font-medium transition-colors duration-200 flex items-center gap-2`}
                    >
                        <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                        Document Converter
                    </button>
                </nav>
            </div>
        </div>
    </div>
);

// Main App Component
export default function App() {
    const [currentPage, setCurrentPage] = useState('agent'); // Default to agent page

    return (
        <div className="bg-black min-h-screen antialiased" style={{ fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", system-ui, sans-serif' }}>
            <Navigation currentPage={currentPage} onPageChange={setCurrentPage} />

            <div className="min-h-screen flex items-center justify-center p-4">
                {currentPage === 'agent' ? (
                    <AgentInterface />
                ) : (
                    <DocumentConverter />
                )}
            </div>
        </div>
    );
}
