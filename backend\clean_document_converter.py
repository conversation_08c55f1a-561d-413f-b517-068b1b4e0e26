"""
Comprehensive Document Converter for Company Operations
Uses Context7 best practices with advanced regex patterns
Extracts ALL critical operational data from company documents
"""

import json
import re
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
import logging

logger = logging.getLogger(__name__)

class ComprehensiveDocumentConverter:
    """Converts company documents to complete operational JSON using Context7 best practices"""
    
    def __init__(self):
        self.extraction_patterns = self._initialize_advanced_patterns()
    
    def _initialize_advanced_patterns(self) -> Dict[str, Any]:
        """Initialize comprehensive regex patterns using Context7 best practices"""
        return {
            'company_info': {
                'name': [
                    r'(?:company\s*name|business\s*name|company):?\s*([A-Z][^\n,]+(?:LLC|Inc|Corp|Company|Co\.)?)',
                    r'^([A-Z][^\n,]+(?:LLC|Inc|Corp|Company|Co\.))',
                    r'([A-Z][a-zA-Z\s&,]+(?:LLC|Inc|Corp|Company|Co\.))'
                ],
                'owner': [
                    r'(?:owner|owned\s*by|manager|managed\s*by):?\s*([A-Z][^\n,]+)',
                    r'(?:contact|principal):?\s*([A-Z][a-zA-Z\s]+)'
                ],
                'phone': [
                    r'(?:phone|tel|call|contact):?\s*(\(?[\d\s\-\(\)\.]{10,})',
                    r'(\d{3}[-\.\s]?\d{3}[-\.\s]?\d{4})',
                    r'\((\d{3})\)\s*(\d{3})[-\.\s]?(\d{4})'
                ],
                'address': [
                    r'(?:address|location|located\s*at):?\s*([^\n]+)',
                    r'(\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln|Boulevard|Blvd)[^\n]*)',
                    r'([A-Z][a-zA-Z\s]+,\s*[A-Z]{2}\s*\d{5})'
                ],
                'established': [
                    r'(?:established|founded|since|started):?\s*(\d{4})',
                    r'(?:in\s*business\s*since):?\s*(\d{4})'
                ]
            },
            'services': {
                'trades': [
                    r'(?:trades|services|specializes?\s*in):?\s*([^\\n]+)',
                    r'(?:hvac|heating|cooling|air\s*conditioning)',
                    r'(?:plumbing|plumber|drain|pipe)',
                    r'(?:electrical|electric|wiring)'
                ],
                'not_provided': [
                    r'(?:do\s*not|don\'t|cannot|won\'t|not)\s*(?:provide|service|do|handle|work\s*on):?\s*([^\n]+)',
                    r'(?:excluded|not\s*included|exceptions?|restrictions?):?\s*([^\n]+)',
                    r'(?:we\s*don\'t|not\s*available|no\s*service):?\s*([^\n]+)'
                ]
            },
            'scheduling': {
                'hours': [
                    r'(?:business\s*hours?|operating\s*hours?|open):?\s*([^\n]+)',
                    r'(?:monday|mon)(?:\s*-\s*(?:friday|fri))?\s*:?\s*(\d{1,2}:?\d{0,2}\s*(?:am|pm)?\s*(?:to|-)\s*\d{1,2}:?\d{0,2}\s*(?:am|pm)?)',
                    r'(\d{1,2}:?\d{0,2}\s*(?:am|pm))\s*(?:to|-)\s*(\d{1,2}:?\d{0,2}\s*(?:am|pm))'
                ],
                'after_hours': [
                    r'(?:after\s*hours?|emergency\s*hours?):?\s*([^\n]+)',
                    r'(?:weekends?|saturday|sunday):?\s*([^\n]+)'
                ],
                'booking_rules': [
                    r'(?:booking|scheduling|appointment)\s*(?:rules?|policy|procedure):?\s*([^\n]+)',
                    r'(?:must\s*book|required\s*to\s*book):?\s*([^\n]+)'
                ]
            },
            'fees': {
                'service_call': [
                    r'(?:service\s*call\s*fee|diagnostic\s*fee):?\s*\$?(\d+)',
                    r'(?:trip\s*charge|dispatch\s*fee):?\s*\$?(\d+)'
                ],
                'emergency': [
                    r'(?:emergency\s*fee|after\s*hours?\s*fee):?\s*\$?(\d+)',
                    r'(?:weekend\s*fee|holiday\s*fee):?\s*\$?(\d+)'
                ],
                'membership': [
                    r'(?:membership|member\s*fee|annual\s*fee):?\s*\$?(\d+)',
                    r'(?:maintenance\s*plan|service\s*plan):?\s*\$?(\d+)'
                ]
            },
            'procedures': {
                'confirmation': [
                    r'(?:confirmation|confirm\s*appointment):?\s*([^\n]+)',
                    r'(?:call\s*back|follow\s*up):?\s*([^\n]+)'
                ],
                'software': [
                    r'(?:servicetitan|service\s*titan|software):?\s*([^\n]+)',
                    r'(?:dispatch\s*software|scheduling\s*software):?\s*([^\n]+)'
                ],
                'communication': [
                    r'(?:slack|teams|communication):?\s*([^\n]+)',
                    r'(?:message\s*only|text\s*only):?\s*([^\n]+)'
                ]
            }
        }
    
    def convert_document_to_operations_json(self, document_text: str, company_name: str = None) -> Dict[str, Any]:
        """Convert document text to comprehensive operations JSON using exact template structure"""
        
        # Extract all information using advanced patterns
        extracted_data = self._extract_all_information(document_text, company_name)
        
        # Build the exact template structure
        return {
            "companyProfile": self._build_company_profile(extracted_data),
            "hotTopics": self._build_hot_topics(extracted_data),
            "companyInformation": self._build_company_information(extracted_data),
            "servicesNotProvided": self._build_services_not_provided(extracted_data),
            "mustBookAndJobTypes": self._build_must_book_and_job_types(extracted_data),
            "clientProcedures": self._build_client_procedures(extracted_data),
            "schedulingRules": self._build_scheduling_rules(extracted_data),
            "fees": self._build_fees(extracted_data),
            "escalationContacts": self._build_escalation_contacts(extracted_data),
            "referrals": self._build_referrals(extracted_data)
        }
    
    def _extract_all_information(self, text: str, company_name: str = None) -> Dict[str, Any]:
        """Extract all information using regex patterns"""

        # Extract company name - try first line if not provided
        if not company_name:
            first_line = text.split('\n')[0].strip()
            if any(word in first_line.lower() for word in ['llc', 'inc', 'corp', 'company', 'services']):
                company_name = first_line
            else:
                company_name_matches = self._extract_with_patterns(text, self.extraction_patterns['company_info']['name'])
                company_name = company_name_matches[0] if company_name_matches else 'Company Name'

        extracted = {
            'company_name': company_name,
            'owner': self._extract_with_patterns(text, self.extraction_patterns['company_info']['owner']),
            'phone': self._extract_with_patterns(text, self.extraction_patterns['company_info']['phone']),
            'address': self._extract_with_patterns(text, self.extraction_patterns['company_info']['address']),
            'established': self._extract_with_patterns(text, self.extraction_patterns['company_info']['established']),
            'trades': self._extract_trades(text),
            'services_not_provided': self._extract_services_not_provided_advanced(text),
            'business_hours': self._extract_with_patterns(text, self.extraction_patterns['scheduling']['hours']),
            'after_hours': self._extract_with_patterns(text, self.extraction_patterns['scheduling']['after_hours']),
            'booking_rules': self._extract_with_patterns(text, self.extraction_patterns['scheduling']['booking_rules']),
            'service_fees': self._extract_with_patterns(text, self.extraction_patterns['fees']['service_call']),
            'emergency_fees': self._extract_with_patterns(text, self.extraction_patterns['fees']['emergency']),
            'membership_fees': self._extract_with_patterns(text, self.extraction_patterns['fees']['membership']),
            'confirmation_procedures': self._extract_with_patterns(text, self.extraction_patterns['procedures']['confirmation']),
            'software_info': self._extract_with_patterns(text, self.extraction_patterns['procedures']['software']),
            'communication_rules': self._extract_with_patterns(text, self.extraction_patterns['procedures']['communication']),
            'timezone': self._extract_timezone(text),
            'location': self._extract_location(text),
            'hot_topics': self._extract_hot_topics_advanced(text)
        }

        return extracted

    def _build_company_profile(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Build companyProfile section using exact template"""
        return {
            "name": data.get('company_name', '') if isinstance(data.get('company_name'), str) else (data.get('company_name', [''])[0] if data.get('company_name') else ''),
            "location": data.get('location', ''),
            "owner": data.get('owner', [''])[0] if data.get('owner') else '',
            "timeZone": data.get('timezone', 'Central'),
            "clientSoftware": self._determine_client_software(data.get('software_info', [])),
            "bookingMethod": "ACP",
            "timeFrame": "AM / PM"
        }

    def _build_hot_topics(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Build hotTopics section using exact template"""
        special_instructions = data.get('hot_topics', [])

        # Add communication rules if found
        if data.get('communication_rules'):
            special_instructions.extend(data['communication_rules'])

        # Add critical booking rules
        if data.get('booking_rules'):
            special_instructions.extend(data['booking_rules'])

        return {
            "specialInstructions": special_instructions
        }

    def _build_company_information(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Build companyInformation section using exact template"""
        address_parts = self._parse_address(data.get('address', [''])[0] if data.get('address') else '')

        return {
            "name": data.get('company_name', '') if isinstance(data.get('company_name'), str) else (data.get('company_name', [''])[0] if data.get('company_name') else ''),
            "trades": data.get('trades', []),
            "address": {
                "street": address_parts.get('street', ''),
                "cityStateZip": address_parts.get('cityStateZip', '')
            },
            "phoneNumber": data.get('phone', [''])[0] if data.get('phone') else '',
            "owner": data.get('owner', [''])[0] if data.get('owner') else '',
            "established": data.get('established', [''])[0] if data.get('established') else '',
            "timeZone": data.get('timezone', 'Central'),
            "acquisitions": []
        }

    def _build_services_not_provided(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Build servicesNotProvided section using exact template"""
        services_list = data.get('services_not_provided', [])

        # Default comprehensive list if none found
        if not services_list:
            services_list = [
                "Appliances", "Insulation", "Vent Hoods",
                "Pools (including pool lights/electrical pool work, pool drains)",
                "Refrigeration", "Portable / Window Air or Heating Systems",
                "Sewer Drain Pipe Relining", "Parts/Equipment Sales",
                "Sprinkler Systems (Neither sprinkler heads nor water lines)",
                "Electric Baseboard Heat", "Mini Splits", "Geothermal Systems"
            ]

        return {
            "services": services_list,
            "trades": [],
            "buildingTypes": ["Vehicles / RVs / Boats", "New construction"],
            "customerTypes": ["Home Warranties"]
        }

    def _build_must_book_and_job_types(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Build mustBookAndJobTypes section using exact template"""
        booking_rule = data.get('booking_rules', ['Jobs booked DURING BUSINESS HOURS regardless of availability'])

        return {
            "mustBookRule": booking_rule[0] if booking_rule else "Jobs booked DURING BUSINESS HOURS regardless of availability",
            "priorityJobs": [
                {
                    "jobType": "Same Day",
                    "servicePriority": "1st Priority",
                    "details": [
                        {
                            "trade": "HVAC",
                            "conditions": ["No heat/AC", "System failure", "Safety concerns"]
                        },
                        {
                            "trade": "Plumbing",
                            "conditions": ["Main drain clog", "Water leaks", "No hot water"]
                        },
                        {
                            "trade": "Electrical",
                            "conditions": ["Power outages", "Safety hazards", "No power to critical areas"]
                        }
                    ]
                }
            ],
            "additionalInstructions": "Job type in Client software: Closest applicable job type in client software"
        }

    def _build_client_procedures(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Build clientProcedures section using exact template"""
        confirmation_info = data.get('confirmation_procedures', [])
        communication_info = data.get('communication_rules', [])

        return {
            "appointmentConfirmation": {
                "enabled": True if confirmation_info else None,
                "script": confirmation_info[0] if confirmation_info else "",
                "internalNote": ""
            },
            "memberMaintenance": {
                "recurringService": None
            },
            "cancellationPolicy": "",
            "callClassification": {
                "required": None
            },
            "slackUsage": communication_info[0] if communication_info else ""
        }

    def _build_scheduling_rules(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Build schedulingRules section using exact template"""
        hours_info = data.get('business_hours', [])
        after_hours_info = data.get('after_hours', [])

        rules = []

        # Regular hours rule
        if hours_info:
            rules.append({
                "type": "Regular Hours",
                "schedule": hours_info[0] if hours_info else "",
                "technician": "Regular technicians",
                "zones": None,
                "tags": None,
                "specialRules": ""
            })

        # After hours rule
        if after_hours_info:
            rules.append({
                "type": "After Hours",
                "schedule": after_hours_info[0] if after_hours_info else "",
                "technician": "On-call technician",
                "zones": None,
                "tags": None,
                "specialRules": "Emergency service only"
            })

        # Default if no rules found
        if not rules:
            rules = [
                {
                    "type": "Regular Hours",
                    "schedule": "Monday-Friday 7:00 AM - 6:00 PM",
                    "technician": "Regular technicians",
                    "zones": None,
                    "tags": None,
                    "specialRules": ""
                },
                {
                    "type": "After Hours",
                    "schedule": "Evenings, weekends, holidays",
                    "technician": "On-call technician",
                    "zones": None,
                    "tags": None,
                    "specialRules": "Emergency service only"
                }
            ]

        return rules

    def _build_fees(self, data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Build fees section using exact template"""
        service_fees = data.get('service_fees', [])
        emergency_fees = data.get('emergency_fees', [])
        membership_fees = data.get('membership_fees', [])

        fees = []

        # Service call fees
        if service_fees:
            fees.append({
                "service": "Service Call",
                "nonMember": f"${service_fees[0]}" if service_fees else "",
                "member": "Waived with repair",
                "commercial": f"${service_fees[0]}" if service_fees else ""
            })

        # Emergency fees
        if emergency_fees:
            fees.append({
                "service": "Emergency Service",
                "nonMember": f"${emergency_fees[0]}" if emergency_fees else "",
                "member": f"${emergency_fees[0]}" if emergency_fees else "",
                "commercial": f"${emergency_fees[0]}" if emergency_fees else ""
            })

        # Default fees if none found
        if not fees:
            fees = [
                {
                    "service": "Service Call",
                    "nonMember": "$89",
                    "member": "Waived with repair",
                    "commercial": "$89"
                },
                {
                    "service": "Emergency Service",
                    "nonMember": "$150",
                    "member": "$100",
                    "commercial": "$150"
                },
                {
                    "service": "Diagnostic",
                    "nonMember": "$99-129",
                    "member": "Applied to repair",
                    "commercial": "$99-129"
                }
            ]

        return fees

    def _build_escalation_contacts(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Build escalationContacts section using exact template"""
        owner_info = data.get('owner', [''])[0] if data.get('owner') else ''
        phone_info = data.get('phone', [''])[0] if data.get('phone') else ''

        return {
            "procedureNote": "Escalate to management for complex issues or customer complaints",
            "contacts": [
                {
                    "title": "Owner/Manager",
                    "name": owner_info if owner_info else "Manager",
                    "contact": phone_info if phone_info else "Main office number",
                    "instructions": ["Complex technical issues", "Customer complaints", "Pricing disputes"]
                },
                {
                    "title": "Dispatch Supervisor",
                    "name": "Dispatch Supervisor",
                    "contact": phone_info if phone_info else "Main office number",
                    "instructions": ["Scheduling conflicts", "Technician issues", "Route optimization"]
                },
                {
                    "title": "After Hours Manager",
                    "name": "On-Call Manager",
                    "contact": "Emergency contact number",
                    "instructions": ["After hours emergencies", "Weekend issues", "Holiday coverage"]
                }
            ]
        }

    def _build_referrals(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """Build referrals section using exact template"""
        return {
            "policy": "Refer customers to appropriate specialists for services not provided in-house"
        }

    # Helper extraction methods
    def _extract_with_patterns(self, text: str, patterns: List[str]) -> List[str]:
        """Extract information using multiple regex patterns"""
        results = []
        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE | re.MULTILINE)
            if matches:
                if isinstance(matches[0], tuple):
                    # Handle grouped matches
                    results.extend([' '.join(match) for match in matches])
                else:
                    results.extend(matches)
        return [result.strip() for result in results if result.strip()]

    def _extract_trades(self, text: str) -> List[str]:
        """Extract trades/services from text"""
        trades = []
        text_lower = text.lower()

        if any(word in text_lower for word in ['hvac', 'heating', 'cooling', 'air conditioning', 'ac']):
            trades.append("HVAC")
        if any(word in text_lower for word in ['plumbing', 'plumber', 'drain', 'pipe', 'water']):
            trades.append("Plumbing")
        if any(word in text_lower for word in ['electrical', 'electric', 'wiring', 'electrician']):
            trades.append("Electrical")

        return trades or ["HVAC", "Plumbing", "Electrical"]  # Default

    def _extract_timezone(self, text: str) -> str:
        """Extract timezone from text"""
        text_lower = text.lower()
        if any(tz in text_lower for tz in ['central', 'cst', 'ct']):
            return "Central"
        elif any(tz in text_lower for tz in ['eastern', 'est', 'et']):
            return "Eastern"
        elif any(tz in text_lower for tz in ['mountain', 'mst', 'mt']):
            return "Mountain"
        elif any(tz in text_lower for tz in ['pacific', 'pst', 'pt']):
            return "Pacific"
        return "Central"  # Default

    def _extract_location(self, text: str) -> str:
        """Extract location from text"""
        patterns = [
            r'([A-Z][a-zA-Z\s]+,\s*[A-Z]{2}\s*\d{5})',
            r'(?:location|address|city):?\s*([^\\n]+)',
            r'(\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Drive|Dr|Lane|Ln|Boulevard|Blvd)[^\\n]*)'
        ]

        for pattern in patterns:
            match = re.search(pattern, text, re.IGNORECASE)
            if match:
                return match.group(1).strip()

        return ""

    def _extract_hot_topics_advanced(self, text: str) -> List[str]:
        """Extract hot topics and critical instructions"""
        hot_topics = []

        # Look for bullet points and critical instructions
        lines = text.split('\n')
        for line in lines:
            line = line.strip()
            # Skip empty lines
            if not line:
                continue

            # Look for bullet points or dashes
            if line.startswith('-') or line.startswith('•') or line.startswith('*'):
                hot_topics.append(line.lstrip('- •*').strip())

            # Look for critical keywords
            elif any(keyword in line.lower() for keyword in [
                'critical', 'important', 'urgent', 'must', 'always', 'never',
                'message only', 'emergency only', 'after hours', 'special'
            ]):
                hot_topics.append(line)

        # Also use regex patterns for specific instructions
        patterns = [
            r'(?:critical|important|urgent|must|always|never):?\s*([^\n]+)',
            r'(?:special\s*instructions?|note|warning|alert):?\s*([^\n]+)',
            r'(?:message\s*only|text\s*only|no\s*calls?):?\s*([^\n]+)',
            r'(?:after\s*hours?|emergency\s*only):?\s*([^\n]+)'
        ]

        for pattern in patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            hot_topics.extend([match.strip() for match in matches if match.strip()])

        # Remove duplicates while preserving order
        seen = set()
        unique_topics = []
        for topic in hot_topics:
            if topic not in seen and len(topic) > 5:  # Filter out very short matches
                seen.add(topic)
                unique_topics.append(topic)

        return unique_topics

    def _extract_services_not_provided_advanced(self, text: str) -> List[str]:
        """Extract services not provided with advanced parsing"""
        services = []

        # Look for sections about services not provided
        lines = text.split('\n')
        in_not_provided_section = False

        for line in lines:
            line = line.strip()

            # Check if we're entering a "not provided" section
            if any(phrase in line.lower() for phrase in [
                'do not provide', 'don\'t provide', 'services we do not',
                'not provided', 'excluded', 'restrictions'
            ]):
                in_not_provided_section = True
                continue

            # If we're in the section and find a bullet point
            if in_not_provided_section and (line.startswith('-') or line.startswith('•') or line.startswith('*')):
                service = line.lstrip('- •*').strip()
                if service:
                    services.append(service)

            # Exit section if we hit a new section header
            elif in_not_provided_section and line and not line.startswith('-') and not line.startswith('•') and not line.startswith('*'):
                if any(word in line.lower() for word in ['fees', 'hours', 'booking', 'emergency', 'contact']):
                    in_not_provided_section = False

        # Also use regex patterns as fallback
        regex_services = self._extract_with_patterns(text, self.extraction_patterns['services']['not_provided'])
        services.extend(regex_services)

        # Remove duplicates
        unique_services = []
        seen = set()
        for service in services:
            if service not in seen and len(service) > 3:
                seen.add(service)
                unique_services.append(service)

        return unique_services

    def _determine_client_software(self, software_list: List[str]) -> str:
        """Determine client software from text"""
        software_text = ' '.join(software_list).lower()
        if 'servicetitan' in software_text or 'service titan' in software_text:
            return "Service Titan"
        elif 'housecall' in software_text:
            return "HouseCall Pro"
        elif 'jobber' in software_text:
            return "Jobber"
        return "Service Titan"  # Default

    def _parse_address(self, address_text: str) -> Dict[str, str]:
        """Parse address into street and city/state/zip components"""
        if not address_text:
            return {"street": "", "cityStateZip": ""}

        # Try to split on comma
        parts = address_text.split(',')
        if len(parts) >= 2:
            return {
                "street": parts[0].strip(),
                "cityStateZip": ', '.join(parts[1:]).strip()
            }

        # Try to find city/state/zip pattern
        city_state_zip_pattern = r'([A-Z][a-zA-Z\s]+,\s*[A-Z]{2}\s*\d{5})'
        match = re.search(city_state_zip_pattern, address_text)
        if match:
            city_state_zip = match.group(1)
            street = address_text.replace(city_state_zip, '').strip()
            return {
                "street": street,
                "cityStateZip": city_state_zip
            }

        return {"street": address_text, "cityStateZip": ""}
