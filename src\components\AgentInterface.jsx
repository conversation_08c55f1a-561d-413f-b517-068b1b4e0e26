import React, { useState, useEffect, useRef } from 'react';

const API_BASE_URL = 'http://localhost:5000';

// File Upload Component
const FileUpload = ({ onFileUpload, disabled }) => {
    const [isDragOver, setIsDragOver] = useState(false);

    const handleFileSelect = (event) => {
        const file = event.target.files[0];
        if (file) {
            onFileUpload(file);
        }
    };

    const handleDrop = (event) => {
        event.preventDefault();
        setIsDragOver(false);
        const file = event.dataTransfer.files[0];
        if (file) {
            onFileUpload(file);
        }
    };

    const handleDragOver = (event) => {
        event.preventDefault();
        setIsDragOver(true);
    };

    const handleDragLeave = () => {
        setIsDragOver(false);
    };

    return (
        <div
            className={`border-2 border-dashed rounded-xl p-6 text-center transition-all duration-200 ${
                isDragOver
                    ? 'border-[#22c55e] bg-[#22c55e]/5'
                    : 'border-[#181818] hover:border-[#222]'
            } ${disabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
            onDrop={handleDrop}
            onDragOver={handleDragOver}
            onDragLeave={handleDragLeave}
        >
            <input
                type="file"
                accept=".json"
                onChange={handleFileSelect}
                className="hidden"
                id="file-upload"
                disabled={disabled}
            />
            <label htmlFor="file-upload" className={disabled ? 'cursor-not-allowed' : 'cursor-pointer'}>
                <div className="flex flex-col items-center gap-2">
                    <svg className="w-8 h-8 text-[#666]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                    </svg>
                    <div className="text-sm text-[#888]">
                        <span className="text-[#22c55e] font-medium">Click to upload</span> or drag and drop
                    </div>
                    <div className="text-xs text-[#666]">JSON files only</div>
                </div>
            </label>
        </div>
    );
};

// Chat Message Component
const ChatMessage = ({ message, isUser, isLoading }) => {
    return (
        <div className={`flex gap-4 p-4 animate-fade-in ${!isUser ? 'bg-[#111] rounded-2xl shadow-lg shadow-black/10' : ''}`}>
            <div className={`w-9 h-9 rounded-xl flex items-center justify-center flex-shrink-0 shadow-sm shadow-black/20 ${
                isUser
                    ? 'bg-gradient-to-br from-indigo-500 to-purple-500'
                    : 'bg-gradient-to-br from-[#222] to-[#333]'
            }`}>
                {isUser ? (
                    <span className="text-white text-sm font-medium">U</span>
                ) : (
                    <span className="text-white text-sm font-medium">AI</span>
                )}
            </div>
            <div className="flex-1 min-w-0">
                {isLoading ? (
                    <div className="flex items-center gap-2 text-[#666] animate-pulse">
                        <svg className="animate-spin w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                            <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h12v11H4V4z" clipRule="evenodd" />
                        </svg>
                        <span className="text-sm">Thinking...</span>
                    </div>
                ) : (
                    <div className="text-white text-sm leading-relaxed whitespace-pre-wrap">
                        {message}
                    </div>
                )}
            </div>
        </div>
    );
};

// Chat Interface Component
const ChatInterface = ({ companyInfo }) => {
    const [messages, setMessages] = useState([]);
    const [inputValue, setInputValue] = useState('');
    const [isLoading, setIsLoading] = useState(false);
    const messagesEndRef = useRef(null);
    
    const scrollToBottom = () => {
        messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
    };
    
    useEffect(() => {
        scrollToBottom();
    }, [messages]);
    
    const sendMessage = async () => {
        if (!inputValue.trim() || isLoading) return;
        if (!companyInfo?.loaded) {
            alert('Please upload company data first');
            return;
        }
        
        const userMessage = inputValue.trim();
        setInputValue('');
        setMessages(prev => [...prev, { text: userMessage, isUser: true }]);
        setIsLoading(true);
        
        try {
            const response = await fetch(`${API_BASE_URL}/chat`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ message: userMessage }),
            });
            
            const data = await response.json();
            
            if (response.ok) {
                setMessages(prev => [...prev, { text: data.response, isUser: false }]);
            } else {
                setMessages(prev => [...prev, { text: `Error: ${data.error}`, isUser: false }]);
            }
        } catch (error) {
            setMessages(prev => [...prev, { text: `Error: ${error.message}`, isUser: false }]);
        } finally {
            setIsLoading(false);
        }
    };
    
    const handleKeyPress = (e) => {
        if (e.key === 'Enter' && !e.shiftKey) {
            e.preventDefault();
            sendMessage();
        }
    };
    
    return (
        <div className="bg-[#111] rounded-2xl shadow-lg shadow-black/10 flex flex-col h-[800px]">
            <div className="p-6 border-b border-[#181818]">
                <h3 className="text-lg font-semibold text-white tracking-tight">🔴 LIVE Call Assistant</h3>
                <p className="text-[#888] text-sm mt-1">Real-time scheduling guidance while on customer calls</p>
            </div>
            
            <div className="flex-1 overflow-y-auto p-4 space-y-4">
                {messages.length === 0 && (
                    <div className="text-center py-12">
                        <div className="text-[#666] text-sm">
                            {companyInfo?.loaded ? (
                                <>
                                    <p className="mb-2">🎯 Ready to assist with {companyInfo.company_name}</p>
                                    <p>Ask about scheduling, policies, or emergency procedures</p>
                                </>
                            ) : (
                                <p>Upload company data to start chatting</p>
                            )}
                        </div>
                    </div>
                )}
                
                {messages.map((message, index) => (
                    <ChatMessage 
                        key={index} 
                        message={message.text} 
                        isUser={message.isUser}
                    />
                ))}
                
                {isLoading && (
                    <ChatMessage isLoading={true} />
                )}
                
                <div ref={messagesEndRef} />
            </div>
            
            <div className="p-4 border-t border-[#181818]">
                {/* Quick Action Buttons */}
                <div className="mb-3 flex flex-wrap gap-2">
                    <button
                        onClick={() => setInputValue("What's our current time and booking rules?")}
                        className="bg-[#222] hover:bg-[#333] text-[#22c55e] px-3 py-1 rounded-lg text-xs font-medium transition-all duration-200"
                    >
                        ⏰ Current Time
                    </button>
                    <button
                        onClick={() => setInputValue("multiple drains clogged not draining")}
                        className="bg-[#222] hover:bg-[#333] text-[#22c55e] px-3 py-1 rounded-lg text-xs font-medium transition-all duration-200"
                    >
                        🚰 Drain Clogs
                    </button>
                    <button
                        onClick={() => setInputValue("customer has no heat emergency")}
                        className="bg-[#222] hover:bg-[#333] text-[#22c55e] px-3 py-1 rounded-lg text-xs font-medium transition-all duration-200"
                    >
                        🔥 No Heat
                    </button>
                </div>
                
                {/* Input Area */}
                <div className="flex gap-3">
                    <textarea
                        value={inputValue}
                        onChange={(e) => setInputValue(e.target.value)}
                        onKeyPress={handleKeyPress}
                        placeholder={companyInfo?.loaded ? "Describe the customer's situation..." : "Upload company data first"}
                        className="flex-1 bg-[#222] rounded-xl p-3 text-white text-sm resize-none h-12 focus:outline-none focus:ring-1 focus:ring-[#22c55e] transition-all duration-200"
                        disabled={!companyInfo?.loaded || isLoading}
                    />
                    <button
                        onClick={sendMessage}
                        disabled={!inputValue.trim() || isLoading || !companyInfo?.loaded}
                        className="bg-[#22c55e] hover:bg-[#22c55e]/80 disabled:bg-[#222] disabled:opacity-50 text-white px-4 py-2 rounded-xl font-medium transition-all duration-200 disabled:cursor-not-allowed"
                    >
                        Send
                    </button>
                </div>
            </div>
        </div>
    );
};

// Company Info Display Component
const CompanyInfoDisplay = ({ companyInfo, onClearData }) => {
    if (!companyInfo?.loaded) {
        return (
            <div className="bg-[#111] rounded-2xl p-4 shadow-lg shadow-black/10">
                <h3 className="text-sm font-semibold text-white mb-3 tracking-tight">Company Status</h3>
                <div className="text-center py-6">
                    <div className="text-[#666] text-sm">
                        <p className="mb-2">⚠️ No company data loaded</p>
                        <p>Upload a JSON file to get started</p>
                    </div>
                </div>
            </div>
        );
    }

    return (
        <div className="bg-[#111] rounded-2xl p-4 shadow-lg shadow-black/10">
            <div className="flex items-center justify-between mb-3">
                <h3 className="text-sm font-semibold text-white tracking-tight">Company Status</h3>
                <button
                    onClick={onClearData}
                    className="text-[#666] hover:text-red-400 text-xs transition-colors duration-200"
                >
                    Clear Data
                </button>
            </div>
            <div className="space-y-2">
                <div className="flex items-center gap-2">
                    <div className="w-2 h-2 bg-[#22c55e] rounded-full"></div>
                    <span className="text-white text-sm font-medium">{companyInfo.company_name}</span>
                </div>
                <div className="text-[#888] text-xs">
                    <p>✅ Data loaded successfully</p>
                    <p>📊 {companyInfo.sections_loaded?.length || 0} sections available</p>
                </div>
            </div>
        </div>
    );
};

// Main Agent Interface Component
export default function AgentInterface() {
    const [companyInfo, setCompanyInfo] = useState({ loaded: false });
    const [isUploading, setIsUploading] = useState(false);

    // Load company info on component mount
    useEffect(() => {
        loadCompanyInfo();
    }, []);

    const loadCompanyInfo = async () => {
        try {
            const response = await fetch(`${API_BASE_URL}/company-data`);
            const data = await response.json();

            if (response.ok && data.loaded) {
                setCompanyInfo(data);
            } else {
                setCompanyInfo({ loaded: false });
            }
        } catch (error) {
            console.error('Error loading company info:', error);
            setCompanyInfo({ loaded: false });
        }
    };

    const handleFileUpload = async (file) => {
        if (!file.name.endsWith('.json')) {
            alert('Please upload a JSON file');
            return;
        }

        setIsUploading(true);
        const formData = new FormData();
        formData.append('file', file);

        try {
            const response = await fetch(`${API_BASE_URL}/upload-json`, {
                method: 'POST',
                body: formData,
            });

            const data = await response.json();

            if (response.ok) {
                setCompanyInfo({
                    loaded: true,
                    company_name: data.company_name,
                    sections_loaded: data.sections_loaded
                });
                alert(`✅ Successfully loaded data for: ${data.company_name}`);
            } else {
                alert(`❌ Upload failed: ${data.error}`);
            }
        } catch (error) {
            alert(`❌ Upload error: ${error.message}`);
        } finally {
            setIsUploading(false);
        }
    };

    const handleClearData = async () => {
        if (!confirm('Are you sure you want to clear the company data?')) return;

        try {
            const response = await fetch(`${API_BASE_URL}/clear-data`, {
                method: 'POST',
            });

            if (response.ok) {
                setCompanyInfo({ loaded: false });
                alert('✅ Company data cleared successfully');
            } else {
                alert('❌ Failed to clear data');
            }
        } catch (error) {
            alert(`❌ Error: ${error.message}`);
        }
    };

    return (
        <div className="max-w-7xl mx-auto p-8">
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Left Column - Upload and Company Info */}
                <div className="space-y-6">
                    <div className="bg-[#111] rounded-2xl p-4 shadow-lg shadow-black/10">
                        <h3 className="text-sm font-semibold text-white mb-3 tracking-tight">Upload Company Data</h3>
                        <FileUpload onFileUpload={handleFileUpload} disabled={isUploading} />
                        {isUploading && (
                            <div className="mt-3 text-center">
                                <div className="text-[#888] text-sm flex items-center justify-center gap-2">
                                    <svg className="animate-spin w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fillRule="evenodd" d="M4 2a2 2 0 00-2 2v11a2 2 0 002 2h12a2 2 0 002-2V4a2 2 0 00-2-2H4zm0 2h12v11H4V4z" clipRule="evenodd" />
                                    </svg>
                                    Uploading...
                                </div>
                            </div>
                        )}
                    </div>
                    
                    <CompanyInfoDisplay companyInfo={companyInfo} onClearData={handleClearData} />
                </div>
                
                {/* Right Column - Chat Interface */}
                <div className="lg:col-span-2">
                    <ChatInterface companyInfo={companyInfo} />
                </div>
            </div>
            
            <footer className="text-center mt-8 text-[#666] text-xs">
                <p>Contractor In Charge - Company Operations Agent</p>
            </footer>
        </div>
    );
}
